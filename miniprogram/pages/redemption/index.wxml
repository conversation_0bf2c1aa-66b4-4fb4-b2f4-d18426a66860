<scroll-view
  style="flex: 1"
  show-scrollbar="{{ false }}"
  scroll-y
  enhanced
  refresher-enabled="{{ userInfo ? true : false }}"
  refresher-triggered="{{ refresherStatus }}"
  bindrefresherrefresh="onPullDownRefresh"
>
  <view class="page">
    <custom-image
      wx:if="{{ currentCoupon.imageUrl }}"
      class="top-bg"
      src="{{ currentCoupon.imageUrl }}"
    />

    <t-navbar class="custom-navbar">
      <view slot="left" class="nav-left">
        <block wx:if="{{ userInfo }}">
          <view
            wx:if="{{ currentShop.id }}"
            class="flex items-center nav-location"
            bindtap="onTapStore"
          >
            <block wx:if="{{ currentCoupon.id }}">
              <image class="ic" src="/assets/image/icon/address2.png" />
              <text class="truncate text">{{ currentShop.name }}</text>
              <image class="ic" src="/assets/image/icon/arrow-right2.png" />
            </block>
            <block wx:else>
              <t-icon name="location" size="30rpx" color="#333" />
              <text class="truncate text text-none">{{ currentShop.name }}</text>
              <image class="ic" src="/assets/image/icon/arrow-right.png" />
            </block>
          </view>
          <view wx:else class="flex items-center nav-location">
            <text class="text text-none">暂无门店</text>
          </view>
        </block>
        <view wx:else class="flex items-center nav-not-login" bindtap="onTapLogin">
          <image class="ic" src="/assets/image/icon/drawer.png" />
          <text class="text">未登录</text>
        </view>
      </view>
    </t-navbar>
    <block wx:if="{{ currentCoupon.id }}">
      <view class="top-box-wrap">
        <view class="flex items-center justify-between name">
          <text class="truncate tt">{{ currentCoupon.title }}*1{{ currentCoupon.unitsText }}</text>
          <view class="flex items-center text" bindtap="onTapRecord">
            <text class="use-record">使用记录</text>
            <image class="ic" src="/assets/image/icon/arrow-right.png" />
          </view>
        </view>
        <view class="item">
          <text class="label">限制：</text>
          <text>{{ currentCoupon.usedLimitsText }}</text>
        </view>
        <view class="item" bindtap="onShowRule">
          <text class="label">须知：</text>
          <text>{{ currentCoupon.usedTimeLimitsText }}</text>
          <image class="ic" src="/assets/image/icon/arrow-right.png" />
        </view>
        <view class="item">
          <text class="label">价值：</text>
          <text>¥ {{ currentCoupon.retailPrice }}</text>
        </view>
      </view>
      <view class="card-box-wrap">
        <view class="card-box">
          <view class="tip">
            {{ currentCoupon.lockStatus == 1 ? '使用时向商家出示' : '未解锁暂不能使用' }}
          </view>
          <view class="flex items-center justify-between coupon-block">
            <image
              wx:if="{{ couponList.length > 1 }}"
              class="btn"
              src="{{ prevBtnDisabled ? ic_prev : ic_prev_active }}"
              bindtap="onPrev"
            />
            <swiper
              class="swiper-coupon"
              current="{{ currentCouponIndex }}"
              layout-type="transformer"
            >
              <!-- current="{{ currentCoupon.index }}" -->
              <!-- bindchange="onChangeSwiper" -->
              <swiper-item
                wx:for="{{ couponList }}"
                wx:key="id"
                class="swiper-item"
                bindchange="onSwiperChange"
                catchtouchmove="catchTouchMove"
              >
                <block wx:if="{{ item.lockStatus == 1 }}">
                  <qrcode-generator
                    wx:if="{{ currentCouponIndex === index }}"
                    class="img"
                    text="{{ item.qrcodeUrl }}"
                    error-correction-level="L"
                    margin="6"
                    enable-preview="{{ true }}"
                  />
                </block>
                <!-- <view wx:if="{{ item.lockStatus === 1 }}" class="img">{{ item.lockStatus }}</view> -->
                <!-- <view wx:if="{{ currentCoupon.lockStatus === 1 }}" class="img">111</view> -->

                <view wx:else class="img">
                  <image class="qrcode" src="/assets/image/illus/default-qrcode.png" />
                  <view class="flex items-center justify-center img-lock">
                    <image class="ic" src="/assets/image/icon/lock.svg" />
                  </view>
                </view>
              </swiper-item>
            </swiper>
            <image
              wx:if="{{ couponList.length > 1 }}"
              class="btn"
              src="{{ nextBtnDisabled ? ic_next : ic_next_active }}"
              bindtap="onNext"
            />
          </view>
          <view class="flex items-center justify-center coupon-text">
            <text>券码</text>
            <text class="val">{{ currentCoupon.couponNo }}</text>
          </view>
          <view class="flex items-end justify-center coupon-page">
            <text class="current">{{ currentCoupon._index + 1 }}</text>
            <text class="total">/ {{ couponList.length }}</text>
          </view>
          <view wx:if="{{ currentCoupon.lockStatus == 1 }}" class="invalid-date">
            {{ currentCoupon.endTime }}到期
          </view>
          <view wx:else class="invalid-date">{{ currentCoupon.unlockableTime }}自动解锁</view>

          <view
            wx:if="{{ currentCoupon.lockStatus == 1 }}"
            class="flex items-center justify-between btns"
          >
            <view class="btn-item" bindtap="onTapShare">
              <view class="flex items-center justify-center flex-col">
                <image class="ic" src="{{ ic_share }}" />
                <text class="text">赠朋友</text>
              </view>
              <button class="button" data-key="send-friend" open-type="share"></button>
            </view>
            <view
              class="flex items-center justify-center flex-col btn-item"
              bindtap="onTapLocation"
            >
              <image class="ic" src="{{ ic_navigation }}" />
              <text class="text">{{ currentCoupon.distance || 0 }}km</text>
            </view>
            <view class="flex items-center justify-center flex-col btn-item" bindtap="onCallStore">
              <image class="ic" src="{{ ic_phone }}" />
              <text class="text">商家</text>
            </view>
          </view>
          <view wx:else class="btn-unlock" bindtap="onTapUnlock">立即解锁</view>
        </view>
      </view>
    </block>
    <block wx:else>
      <view class="placeholder-box">
        <image class="img" src="/assets/image/illus/coupon.png" />
        <text class="text">暂无记录</text>
      </view>
    </block>
  </view>
</scroll-view>

<t-popup
  visible="{{ showLockRulePopup }}"
  bind:visible-change="onLockVisibleChange"
  placement="center"
  close-on-overlay-click
>
  <view class="rule-container">
    <view class="title">解锁说明</view>
    <view class="sub-title">自动解锁</view>
    <view class="sub-content">1、该券将于{{ currentCoupon.unlockableTime }}日会自动解锁</view>
    <view class="sub-content">2、 解锁后有效期为{{ currentCoupon.expireDays }}天（过期作废）</view>
    <view class="sub-title">手动解锁</view>
    <view class="sub-content">1、邀请一个好友购买，可立即解锁</view>
    <view class="sub-content">2、解锁后有效期为{{ currentCoupon.expireDays }}天（过期作废）</view>
    <view class="sub-content">3、每张券均可邀请一个好友进行解锁</view>
    <view class="sub-content">4、已解锁的券也可以赠送朋友使用</view>
    <view class="tips">已解锁的抵用券请在核销中查看</view>
    <view class="btn-invite">
      <view class="flex items-center justify-center block">
        <t-icon name="logo-wechat-stroke" size="32rpx" color="#333" />
        <text class="text">立即邀请</text>
      </view>
      <button class="button" data-key="invite" open-type="share"></button>
    </view>
    <t-icon
      t-class="close-btn"
      name="close-circle"
      size="64rpx"
      color="#fff"
      bind:tap="onCloseLockRulePopup"
    />
  </view>
</t-popup>

<t-popup
  visible="{{ showRulePopup }}"
  bind:visible-change="onVisibleChange"
  placement="center"
  close-on-overlay-click
>
  <view class="rule-container2">
    <view class="title">购买须知</view>
    <view class="sub-title">使用限制</view>
    <view class="sub-content">{{ currentCoupon.usedLimitsText }}</view>
    <view class="sub-title">使用时间</view>
    <view class="sub-content">{{ currentCoupon.usedTimeLimitsText }}</view>
    <view class="sub-title">使用规则</view>
    <text class="sub-content" space="ensp">{{ currentCoupon.usedRules }}</text>
    <t-icon
      t-class="close-btn"
      name="close-circle"
      size="64rpx"
      color="#fff"
      bind:tap="onCloseRulePopup"
    />
  </view>
</t-popup>
