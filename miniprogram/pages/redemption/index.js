import { couponList, couponShopList } from '@/api/index';
import { APP_DOMAIN } from '@/config-global';
import { useLocation } from '@/utils/location';
import { getImageUrl } from '@/utils/format';

const app = getApp();

Page({
  isFirstLoad: true,
  data: {
    ic_share: '/assets/image/icon/share.png',
    ic_navigation: '/assets/image/icon/navigation.png',
    ic_phone: '/assets/image/icon/phone2.png',
    ic_prev: '/assets/image/icon/prev.png',
    ic_prev_active: '/assets/image/icon/prev-active.png',
    ic_next: '/assets/image/icon/next.png',
    ic_next_active: '/assets/image/icon/next-active.png',
    userInfo: null,
    storeInfo: {},
    locationInfo: {},
    couponList: [],
    currentCoupon: {},
    currentShop: {},
    currentCouponIndex: 0,
    showLockRulePopup: false,
    showRulePopup: false,
    prevBtnDisabled: true,
    nextBtnDisabled: false,
    refresherStatus: false,
  },
  onLoad() {
    wx.hideShareMenu();
    // this.init();
  },
  onShow() {
    this.init();
    // if (!this.isFirstLoad && app.globalData.shouldReloadTabPage) {
    //   app.globalData.shouldReloadTabPage = false;
    //   this.init();
    // }
  },
  async init() {
    const login = await app.ensureLogin(); // 确保登录

    if (login.success) {
      this.setData({ userInfo: login.data });
      const loc = await useLocation();

      if (loc) {
        this.setData({ locationInfo: loc });
        this.getShopList(loc, (shops) => {
          if (shops && shops.length > 0) {
            let current = shops[0];
            const { currentShop } = this.data;

            if (currentShop && currentShop.id) {
              current = shops.find((e) => e.id === currentShop.id);
            }

            this.setData({ currentShop: current });

            if (current.id) {
              this.getCouponList(current.id, loc);
            }
          }
        });
      }
    }
  },
  async getShopList(loc, cbOK) {
    wx.showLoading({ title: '加载中...' });
    const { longitude, latitude } = loc;

    const res = await couponShopList({ longitude, latitude });
    cbOK(res.data);

    wx.hideLoading({ noConflict: true });
  },
  async getCouponList(shopId, loc) {
    wx.showLoading({ title: '加载中...' });
    const { longitude, latitude } = loc;

    const res = await couponList({ shopId, longitude, latitude });
    if (res.success && res.data) {
      const list = res.data.map((e, i) => ({
        ...e,
        _index: i,
        qrcodeUrl: `${APP_DOMAIN}/q/redeem?cno=${e.couponNo}`,
      }));
      const { currentCouponIndex } = this.data;

      this.setData({
        couponList: list,
        currentCoupon: list[currentCouponIndex || 0],
        // currentCoupon: list[0],
      });
    }
    wx.hideLoading({ noConflict: true });
  },
  onPullDownRefresh() {
    if (!this.data.userInfo) return;

    const { locationInfo: loc, currentShop } = this.data;
    if (loc && currentShop) {
      this.getShopList(loc, (shops) => {
        if (shops && shops.length > 0) {
          let current = shops[0];
          if (currentShop && currentShop.id) {
            current = shops.find((e) => e.id === currentShop.id);
          }

          this.setData({ currentShop: current });
          this.getCouponList(current.id, loc).finally(() => {
            this.setData({ refresherStatus: false });
          });
        } else {
          this.setData({ refresherStatus: false });
        }
      });
    } else {
      this.setData({ refresherStatus: false });
    }
  },
  onSwiperChange(e) {
    const { current } = e.detail;
    const { couponList } = this.data;
    this.setData({
      currentCoupon: couponList[current],
      currentCouponIndex: current,
    });
  },
  onPrev() {
    const { currentCoupon, couponList } = this.data;
    const index = currentCoupon._index - 1;

    if (index >= 0) {
      this.setData({
        currentCouponIndex: index,
        currentCoupon: couponList[index],
        prevBtnDisabled: index === 0, // ✅ 只有当 index 为 0 时才禁用
        nextBtnDisabled: false, // ✅ 只要能往前翻，说明 next 一定可用
      });
    }
  },
  onNext() {
    const { currentCoupon, couponList } = this.data;
    const index = currentCoupon._index + 1;

    if (index < couponList.length) {
      this.setData({
        currentCouponIndex: index,
        currentCoupon: couponList[index],
        nextBtnDisabled: index === couponList.length - 1, // ✅ 只有到最后一个才禁用
        prevBtnDisabled: false, // ✅ 只要能往后翻，说明 prev 一定可用
      });
    }
  },
  onTapRecord() {
    const { currentCoupon } = this.data;
    wx.navigateTo({
      url: `/subpages/redemption/usage-record/index?activityId=${currentCoupon.activityId}`,
    });
  },
  onTapStore() {
    const { longitude, latitude } = this.data.locationInfo;
    wx.navigateTo({
      url: `/subpages/redemption/store-select/index?longitude=${longitude}&latitude=${latitude}`,
      events: {
        acceptStoreInfo: ({ store }) => {
          console.log('store', store);
          if (!store) return;
          wx.showLoading({ title: '切换中...' });

          this.setData({ currentShop: store }, () =>
            this.getCouponList(store.id, this.data.locationInfo)
          );
        },
      },
    });
  },
  onCallStore() {
    const { currentCoupon } = this.data;
    wx.makePhoneCall({ phoneNumber: String(currentCoupon.phone) });
  },
  onTapUnlock() {
    this.setData({ showLockRulePopup: true });
  },
  onCloseLockRulePopup() {
    this.setData({ showLockRulePopup: false });
  },
  onLockVisibleChange(e) {
    this.setData({ showLockRulePopup: e.detail.visible });
  },
  catchTouchMove() {
    return false;
  },
  async onTapLogin() {
    const login = await app.checkLogin();
    if (login.success) {
      this.init();
    }
  },
  onTapLocation() {
    const { longitude, latitude } = this.data.locationInfo;
    wx.openLocation({ latitude, longitude });
  },
  onShowRule() {
    this.setData({ showRulePopup: true });
  },
  onCloseRulePopup() {
    this.setData({ showRulePopup: false });
  },
  onVisibleChange(e) {
    this.setData({ showRulePopup: e.detail.visible });
  },
  onShareAppMessage(e) {
    const { currentCoupon } = this.data;
    const { key } = e.target?.dataset || {};
    const user = app.globalData.userInfo || {};
    let title = '';
    let path = '';

    // if (key == 'send-friend') {
    if (key == 'invite') {
      // 邀请下单 => 活动详情
      title = `我在${currentCoupon.shopName}买了${currentCoupon.title}，才${currentCoupon.activityPrice}元，一单就回本啦`;
      path = `/subpages/home/<USER>/index?uid=${user.id}&id=${currentCoupon.activityId}&cid=${currentCoupon.id}&s=c`;
    } else {
      // 分享优惠券 => 领取优惠券
      title = `我在${currentCoupon.shopName}有1${currentCoupon.unitsText}${currentCoupon.title}免费券送给你，点击后领取`;
      path = `/subpages/share/claim-coupon/index?uid=${user.id}&cid=${currentCoupon.id}&aid=${currentCoupon.activityId}&s=c`;
    }
    console.log(path);

    return { title, path, imageUrl: getImageUrl(currentCoupon.imageUrl) };
  },
});
