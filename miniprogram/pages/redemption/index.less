.page {
  position: relative;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  transition: transform 0.3s ease; /* 动画效果 */
  .top-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 290rpx;
  }
  .top-bg::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
  }
  .nav-left {
    height: 100%;
    width: 100%;
    .nav-not-login {
      height: 100%;
      .ic {
        width: 30rpx;
        height: 30rpx;
      }
      .text {
        font-weight: 500;
        font-size: 28rpx;
        color: #333;
        margin-left: 16rpx;
      }
    }
    .nav-location {
      height: 100%;
      .ic {
        width: 28rpx;
        height: 28rpx;
      }
      .text {
        font-weight: 500;
        font-size: 28rpx;
        color: #fff;
        margin-left: 10rpx;
      }
      .text-none {
        color: #333;
      }
    }
  }

  .top-box-wrap {
    margin-top: 32rpx;
    padding: 32rpx;
    border-top-left-radius: 28rpx;
    border-top-right-radius: 28rpx;
    background-color: #fff;
    .name {
      height: 40rpx;
      font-weight: 500;
      font-size: 40rpx;
      color: #333;
      line-height: 40rpx;
      .tt {
        width: 83%;
      }
      .text {
        height: 24rpx;
        font-size: 24rpx;
        color: #999;
        line-height: 24rpx;
      }
      .ic {
        width: 20rpx;
        height: 20rpx;
      }
      .use-record {
        width: 100rpx;
        text-align: right;
      }
    }
    .item {
      display: flex;
      align-items: center;
      height: 24rpx;
      font-size: 24rpx;
      color: #666;
      line-height: 24rpx;
      margin-top: 16rpx;
      .label {
        font-weight: 500;
      }
      .ic {
        width: 20rpx;
        height: 20rpx;
      }
    }
  }
  .card-box-wrap {
    height: calc(100% - 483rpx);
    padding: 24rpx 32rpx 32rpx;
    background-color: #f2f2f2;

    .card-box {
      display: flex;
      justify-content: center;
      flex-direction: column;
      width: 686rpx;
      height: 100%;
      border-radius: 28rpx;
      margin: 0 auto;
      background-color: #fff;
      .tip {
        height: 28rpx;
        font-size: 28rpx;
        color: #666;
        line-height: 28rpx;
        text-align: center;
        padding-top: 32rpx;
      }
      .coupon-block {
        padding: 0 24rpx;
        .btn {
          width: 56rpx;
          height: 56rpx;
        }

        .swiper-coupon {
          // width: 312rpx;
          // height: 312rpx;
          width: calc(60vh - 483rpx);
          height: calc(60vh - 483rpx);
          margin: 32rpx auto;
          .swiper-item {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            .img {
              position: relative;
              width: 100%;
              height: 100%;
              .qrcode {
                width: 100%;
                height: 100%;
              }
              .img-lock {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.6);
                .ic {
                  width: 56rpx;
                  height: 56rpx;
                }
              }
            }
            .index {
              position: absolute;
              top: 16rpx;
              left: 16rpx;
              color: #333;
            }
          }
        }
      }

      .coupon-text {
        height: 28rpx;
        font-weight: 500;
        font-size: 24rpx;
        color: #999;
        line-height: 28rpx;
        text-align: center;
        .val {
          color: #000;
          margin-left: 8rpx;
        }
      }
      .coupon-page {
        margin-top: 24rpx;
        .current {
          font-weight: 500;
          font-size: 40rpx;
          color: #ff6701;
          line-height: 40rpx;
        }
        .total {
          font-size: 24rpx;
          color: #666;
        }
      }
      .invalid-date {
        height: 28rpx;
        font-size: 28rpx;
        color: #ff6701;
        line-height: 28rpx;
        text-align: center;
        margin-top: 32rpx;
      }

      .btns {
        padding: 48rpx 128rpx 74rpx;

        .btn-item {
          position: relative;
          .ic {
            width: 48rpx;
            height: 48rpx;
          }
          .text {
            height: 24rpx;
            font-size: 24rpx;
            color: #666;
            line-height: 24rpx;
            margin-top: 16rpx;
          }
          .button {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
          }
        }
      }
      .btn-unlock {
        width: 320rpx;
        height: 96rpx;
        border-radius: 48rpx;
        line-height: 96rpx;
        text-align: center;
        font-size: 32rpx;
        color: #fff;
        text-align: center;
        margin: 48rpx auto;
        background-color: #ff6701;
      }
    }
  }

  .placeholder-box {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    width: 100%;
    height: 80%;
    .img {
      width: 378rpx;
      height: 308rpx;
    }
    .text {
      height: 32rpx;
      font-size: 32rpx;
      color: #333;
      line-height: 32rpx;
      margin-top: 48rpx;
    }
  }
}

.rule-container {
  position: relative;
  width: 718rpx;
  background: #ffffff;
  border-radius: 28rpx;
  padding: 80rpx 48rpx 64rpx;
  box-sizing: border-box;
  .title {
    height: 44rpx;
    font-weight: 500;
    font-size: 44rpx;
    color: #333333;
    line-height: 44rpx;
    text-align: center;
    margin-bottom: 48rpx;
  }
  .sub-title {
    height: 28rpx;
    font-weight: 500;
    font-size: 28rpx;
    color: #333333;
    line-height: 28rpx;
    margin-top: 48rpx;
    margin-bottom: 16rpx;
  }
  .sub-content {
    font-size: 28rpx;
    color: #999999;
    line-height: 42rpx;
    // margin-bottom: 48rpx;
  }
  .tips {
    height: 28rpx;
    font-size: 28rpx;
    color: #666666;
    line-height: 28rpx;
    margin-top: 48rpx;
    margin-bottom: 64rpx;
  }
  .btn-invite {
    position: relative;
    width: 544rpx;
    margin: 0 auto;
    .block {
      width: 100%;
      line-height: 96rpx;
      text-align: center;
      border-radius: 48rpx;
      background: linear-gradient(to right, #ffff82, #bafd8f);
      .text {
        margin-left: 16rpx;
        color: #000000;
      }
    }
    .button {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      width: 100%;
      opacity: 0;
    }
  }
  .close-btn {
    position: absolute;
    left: 50%;
    margin-left: -32rpx;
    bottom: calc(-1 * (48rpx + 84rpx));
  }
}

.rule-container2 {
  position: relative;
  width: 718rpx;
  background: #ffffff;
  border-radius: 28rpx;
  padding: 80rpx 48rpx 32rpx;
  box-sizing: border-box;
  .title {
    height: 44rpx;
    font-weight: 500;
    font-size: 44rpx;
    color: #333333;
    line-height: 44rpx;
    text-align: center;
    margin-bottom: 48rpx;
  }
  .sub-title {
    height: 28rpx;
    font-weight: 500;
    font-size: 28rpx;
    color: #333333;
    line-height: 28rpx;
    margin-bottom: 16rpx;
  }
  .sub-content {
    font-size: 28rpx;
    color: #999999;
    line-height: 42rpx;
    margin-bottom: 48rpx;
  }
  .close-btn {
    position: absolute;
    left: 50%;
    margin-left: -32rpx;
    bottom: calc(-1 * (48rpx + 84rpx));
  }
}
