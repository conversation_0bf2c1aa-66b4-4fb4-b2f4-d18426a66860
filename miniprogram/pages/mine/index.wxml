<scroll-view
  scroll-y
  style="flex: 1"
  show-scrollbar="{{ false }}"
  enhanced
  refresher-enabled="{{ userInfo ? true : false }}"
  refresher-triggered="{{ refresherStatus }}"
  bindrefresherrefresh="onPullDownRefresh"
  bindscrolltolower="onReachBottom"
>
  <!-- lower-threshold="0" -->
  <view class="page">
    <image class="top-bg" src="/assets/image/bg/top-gradient.png" />
    <t-navbar class="custom-navbar">
      <!-- <view wx:if="{{ userInfo.id }}" slot="left" class="flex items-center nav-left" bindtap="onTapFeedback" > 反馈 </view>view -->
    </t-navbar>
    <view class="top-box-wrap">
      <view
        wx:if="{{ userInfo }}"
        class="flex items-center justify-between top-box"
        bindtap="onTapUserInfo"
      >
        <view class="flex items-center avatar-box">
          <view class="avatar">
            <custom-image
              class="img"
              src="{{ userInfo.headImg || userInfo.avatarUrl }}"
              radius="50%"
            />
          </view>
          <view class="title">
            <view>{{ userInfo.fullName || userInfo.nickname }}</view>
            <view class="mobile">{{ userInfo.showMobile }}</view>
          </view>
        </view>
        <image class="ic-right" src="/assets/image/icon/arrow-right.png" />
      </view>
      <view wx:else class="flex items-center justify-between top-box" bindtap="onTapLogin">
        <view class="flex items-center avatar-box">
          <view class="avatar">
            <image class="img" src="{{ defaultAvatarUrl }}" />
          </view>
          <view class="title">未登录</view>
        </view>

        <image class="ic-right" src="/assets/image/icon/arrow-right.png" />
      </view>
    </view>

    <view class="tab-wrap">
      <sky-tab
        title=""
        tabList="{{ tabs }}"
        tab-color="#999"
        select-color="#333"
        select-blod="true"
        select-type="line"
        select-bg="#54d601"
        tab-current="{{ selectedTab }}"
        ontouchTab="onChangeTab"
      />
      <view wx:if="{{ !userInfo }}" class="tab-mask" bindtap="onTapLogin"></view>
    </view>
    <!-- wx:if="{{ list.length > 0 }}"  -->
    <view class="order-list">
      <view
        wx:for="{{ list }}"
        wx:key="id"
        class="order-item"
        data-id="{{ item.id }}"
        bindtap="onTapOrderDetail"
      >
        <view class="pic">
          <custom-image class="img" src="{{ item.imageUrl }}" />
          <!-- 订单状态：0-未付款；1-已付款；2-已取消；3-已退款, 详见枚举 OmsOrderStatus	 -->
          <view class="status status-{{ item.status }}">
            {{ item.statusText }}
          </view>
        </view>
        <view class="info">
          <view class="merchant-name">{{ item.shopName }}</view>
          <!-- prettier-ignore -->
          <text class="truncate tips">{{ item.title }}*{{ item.quantity }}{{ item.unitsText }}</text>
          <!-- prettier-ignore -->
          <text class="truncate tips">{{ item.usedLimitsText }}</text>
          <view class="price">
            <view class="symbol">¥</view>
            <view class="val">{{ item.activityPrice }}</view>
          </view>
        </view>
      </view>
    </view>
    <load-more wx:if="{{ pageCount > 1 }}" loading="{{ isLoading }}" is-end="{{ isEnd }}" />
    <custom-empty wx:if="{{ !isLoading && list.length === 0 }}" />
    <!-- <view wx:else class="placeholder-box">
      <image class="img" src="/assets/image/illus/coupon.png" />
      <text class="text">暂无记录</text>
    </view> -->
  </view>
</scroll-view>
