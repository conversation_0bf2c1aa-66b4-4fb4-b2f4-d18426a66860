import { orderList } from '@/api/index';
import { encryptPhoneNumber } from '@/utils/util';
import { DEFAULT_USER_AVATAR } from '@/utils/constant';

const app = getApp();

const tabs = [
  { title: '全部', value: '' },
  { title: '已付款', value: '1' },
  { title: '已退款', value: '3' },
];

Page({
  data: {
    defaultAvatarUrl: DEFAULT_USER_AVATAR,
    userInfo: null,
    selectedTab: 0,
    tabs,
    enums: app.globalData.enums,
    drawerVisible: false,
    list: [],
    pageNo: 1,
    pageSize: 10,
    pageCount: 0,
    isLoading: false,
    isEnd: false,
    refresherStatus: false,
  },
  onLoad() {
    this.init();
  },
  onShow() {
    this.initUser();
  },
  async init() {
    const login = await app.ensureLogin(); // 确保登录

    if (login.success) {
      this.loadData();
    }
  },
  async initUser() {
    const user = app.globalData.userInfo;

    if (user && Number(user.id) !== 0) {
      const userInfo = {
        ...user,
        showMobile: encryptPhoneNumber(user.mobile),
        avatarUrl: user.headImg || user.avatarUrl || user.avatarName || DEFAULT_USER_AVATAR,
      };

      this.setData({ userInfo });
    }
  },
  async loadData(params = {}) {
    const { isEnd, isLoading } = this.data;

    if (isLoading || isEnd) return;
    const pageNo = params.pageNo || this.data.pageNo;

    if (pageNo == 1) {
      wx.showLoading();
    }
    this.setData({ isLoading: true });

    const { pageSize, selectedTab, tabs, list } = this.data;

    const status = tabs[params?.selectedTab || selectedTab].value;

    const res = await orderList({
      status,
      pageNo,
      pageSize,
      ...params,
    });

    if (res.success) {
      const newList = res.data?.list || [];
      const totalCount = pageNo == 1 ? res.data.pageCount : pageCount;

      this.setData({
        list: pageNo == 1 ? newList : list.concat(newList),
        isEnd: pageNo >= totalCount,
        isLoading: false,
        pageCount: totalCount,
        pageNo,
      });
    } else {
      this.setData({ isLoading: false });
    }
    if (pageNo == 1) {
      wx.hideLoading();
    }
  },
  // 上拉加载更多
  onReachBottom() {
    if (!this.data.userInfo) return;

    const { isEnd, pageNo } = this.data;

    if (!isEnd) {
      this.loadData({ pageNo: pageNo + 1 });
    }
  },
  onPullDownRefresh() {
    if (!this.data.userInfo) return;

    this.setData(
      {
        pageNo: 1,
        pageCount: 0,
        isEnd: false,
        isLoading: false,
        pageCount: 0,
      },
      () => {
        this.loadData().finally(() => {
          this.setData({ refresherStatus: false, isRefreshing: false });
        });
      }
    );
  },
  async onTapUserInfo() {
    wx.navigateTo({ url: '/subpages/mine/user-info/index' });
  },
  onTapOrderDetail(e) {
    const { id } = e.currentTarget.dataset;

    wx.navigateTo({
      url: `/subpages/mine/order-detail/index?id=${id}`,
    });
  },
  onTapDrawerBtn() {
    this.setData({
      drawerVisible: !this.data.drawerVisible,
    });
  },
  onDrawerChange(e) {
    const { visible } = e.detail;
    this.setData({
      drawerVisible: visible,
    });
  },
  goStore() {
    wx.navigateTo({
      url: '/subpages/mine/store-manage/index',
    });
  },
  goWallet() {
    wx.navigateTo({
      url: '/subpages/mine/wallet/index',
    });
  },
  goSetting() {
    wx.navigateTo({
      url: '/subpages/mine/setting/index',
    });
  },
  onChangeTab(e) {
    this.setData(
      {
        selectedTab: e.detail.index,
        pageNo: 1,
        isEnd: false,
        isLoading: false,
        pageCount: 0,
      },
      () => this.loadData()
    );
  },
  onTapFeedback() {
    wx.navigateTo({ url: '/subpages/mine/feedback/index' });
  },
  async onTapLogin() {
    const login = await app.checkLogin();
    if (login.success) {
      this.initUser();
      this.loadData();
    }
  },
});
