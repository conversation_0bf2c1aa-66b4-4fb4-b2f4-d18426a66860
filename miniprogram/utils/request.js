import { HOST_API, APP_ID, APP_SECRET, BASE_IMAGE } from '@/config-global';

import { getToken, removeToken, removeUserInfo } from './auth';
import { randomString } from './util';

const md5 = require('@/libs/md5.min.js');

// ----------------------------------------------------------------
const toast = (title, icon = 'none') => wx.showToast({ title, icon });

const handleLogOut = () => {
  removeToken();
  removeUserInfo();
  toast('登录失效，请重新登录');

  setTimeout(() => {
    wx.navigateTo({ url: '/subpages/auth/login/index' });
  }, 300);
};

// 错误处理
const errorHandler = (error) => {
  const { code, message } = error || {};
  const err = error.toString();
  console.log('err', err);

  // token过期，清除本地数据，并跳转至登录页面
  if ([408, 409, 401].includes(code)) {
    handleLogOut();
  } else if (code === 'ECONNABORTED' && message.indexOf('timeout') !== -1) {
    toast('接口请求超时,请刷新页面重试!');
  } else if (err && err.includes('Network Error')) {
    toast('网络异常', 'error');
  } else {
    toast(error.msg || error.message || '服务异常');
  }
};

// 处理请求头部
const getHeadParams = () => {
  const nonceStr = randomString(10);
  const timestamp = new Date().getTime();

  const sign = md5(
    `appId=${APP_ID}&nonceStr=${nonceStr}&timestamp=${timestamp}${APP_SECRET}`
  ).toUpperCase();
  const token = getToken() || '';

  return {
    'Content-Type': 'application/json;charset=utf8',
    appId: APP_ID,
    timestamp,
    nonceStr,
    sign,
    token,
  };
};
const getFullUrl = (url) => HOST_API + url;
/**
 * 请求
 * @param {*} method
 * @param {*} url
 * @param {*} data
 * @param {*} config
 * @returns
 */
const request = async (method, url, data, config = {}) => {
  const mergedConfig = {
    url: getFullUrl(url),
    method,
    header: getHeadParams(),
    data: data || {},
    timeout: 20000,
    ...config,
  };

  return new Promise((resolve) => {
    wx.request({
      ...mergedConfig,
      success: (response) => {
        const { data: response_data } = response || {};
        // let { data: response_data } = response || {};
        // if (url === '/oms/order/createActivityOrder') {
        //   response_data = { code: 100, msg: '您已购买过此活动抵用券' };
        // }
        const isSuccess = response_data?.code === 0;

        // 格式化返回数据
        const res = {
          success: isSuccess,
          data: response_data?.data,
          code: response_data?.code,
          message: response_data?.msg,
        };

        if (!isSuccess) {
          errorHandler(res); // 调用错误处理
        }

        resolve(res);
      },
      fail: (err) => {
        errorHandler(err); // 调用错误处理
        resolve({ success: false, data: null, code: 500, message: '请求失败，请检查网络链接' });
      },
    });
  });
};

/**
 * 上传图片
 * @param {*} data
 * @returns
 */
const uploadImage = async (url, data) => {
  const { filePath } = data;

  if (!filePath) {
    return Promise.resolve({
      success: false,
      data: null,
      message: '请选择图片',
    });
  }

  return new Promise((resolve) => {
    wx.uploadFile({
      url: getFullUrl(url),
      filePath,
      name: 'file',
      header: getHeadParams(),
      success(response) {
        const responseBodyStr = response.data;
        const responseBody = JSON.parse(responseBodyStr);
        if (responseBody && responseBody.data && responseBody.data.url) {
          const result = responseBody.data;

          resolve({
            success: true,
            data: {
              ...result,
              baseImage: BASE_IMAGE,
              url: result.url,
            },
          });
        } else {
          wx.showToast({ title: '上传失败，请联系管理员', icon: 'error' });
          resolve({ success: false, data: responseBody });
        }
      },
      fail(error) {
        resolve({
          success: false,
          data: error,
          message: error.message || error,
        });
      },
    });
  });
};

// 快捷方法
export default {
  // get: (url, params, config) => request('GET', url, params, config),
  post: (url, data, config) => request('POST', url, data, config),
  uploadImage: (url, data) => uploadImage(url, data),
};
