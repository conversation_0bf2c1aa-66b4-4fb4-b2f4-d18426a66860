import { BASE_IMAGE } from '@/config-global';


/**
 * 处理图片路径
 * @param {string} path - 服务端返回的图片路径（可能是完整路径或相对路径）
 * @param {string} [defaultImg=''] - 可选的默认图片路径
 * @returns {string} - 返回完整的图片 URL
 */
export function getImageUrl(path, defaultImg = '') {
  if (!path) {
    return defaultImg;
  }

  // 判断是否是完整 URL（http 或 https 开头）
  const isFullUrl = /^https?:\/\//i.test(path);
  
  if (isFullUrl) {
    return path;
  }

  // 否则拼接全局图片前缀
  return `${BASE_IMAGE}/${path.replace(/^\/+/, '')}`;
}
