/**
 * 检查版本更新信息
 */
export const checkUpdate = () => {
  const updateManager = wx.getUpdateManager();
  // 请求完新版本信息的回调
  updateManager.onCheckForUpdate();

  updateManager.onUpdateReady(function () {
    wx.showModal({
      title: '更新提示',
      content: '新版本已经准备好，是否重启应用？',
      confirmColor: '#54d601',
      success(res) {
        if (res.confirm) {
          // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
          updateManager.applyUpdate();
        }
      },
    });
  });
  // 新版本下载失败
  updateManager.onUpdateFailed();
};

/**
 * 将原始枚举数据转换为高效访问结构
 * @param {Array} originalData 原始数据(需包含name和enumItemList)
 * @returns {Object} 结构为 { [name]: { [value]: text } }
 */
export const createEnumMap = (originalData) => {
  return originalData.reduce((result, { name, enumItemList }) => {
    result[name] = enumItemList.reduce((obj, { value, text }) => {
      obj[value] = text;
      return obj;
    }, {});
    return result;
  }, {});
};

// 全局事件
export const globalEvent = {
  listeners: {},
  // 监听事件
  on(event, callback) {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(callback);
  },
  // 移除事件
  off(event, callback) {
    const callbacks = this.listeners[event];
    if (callbacks) {
      this.listeners[event] = callbacks.filter((cb) => cb !== callback);
    }
  },
  // 触发事件
  emit(event, data) {
    const callbacks = this.listeners[event];
    if (callbacks) {
      callbacks.forEach((cb) => cb(data));
    }
  },
};
