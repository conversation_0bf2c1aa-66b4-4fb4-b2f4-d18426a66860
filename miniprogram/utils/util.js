export function encryptPhoneNumber(phoneNumber) {
  if (!phoneNumber || typeof phoneNumber !== 'string' || phoneNumber.length !== 11) {
    return 'Invalid phone number';
  }
  return phoneNumber.substring(0, 3) + '****' + phoneNumber.substring(7);
}

export function randomString(length) {
  let result = '';
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  const charactersLength = characters.length;
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
}
/**
 * 获取分类数据中最深层级的分类项（优先返回3级分类，其次2级，再1级）。
 * 每个分类项会添加 level 表示其层级。
 *
 * @param {Array} nestedData - 嵌套的分类数组（包含 subCategory 字段）
 * @returns {Array} - 最深层级的分类项数组，带 level 字段
 */
export function getDeepestCategories(nestedData) {
  /**
   * 递归拍平分类数据，并为每项添加层级 level。
   *
   * @param {Array} data - 当前层级的数据数组
   * @param {number} level - 当前层级，初始为1
   * @param {Array} result - 拍平后的结果数组
   * @returns {Array} - 拍平后的结果数组
   */
  function flatten(data, level = 1, result = []) {
    data.forEach((item) => {
      const { subCategory, ...rest } = item; // 分离 subCategory，其余数据保留
      result.push({ ...rest, level }); // 添加当前项并附带层级

      if (subCategory && subCategory.length > 0) {
        // 递归处理子分类，层级+1
        flatten(subCategory, level + 1, result);
      }
    });
    return result;
  }

  // 1. 拍平原始嵌套数据，得到扁平数组并附带 level
  const flatList = flatten(nestedData);

  // 2. 从最深层级3开始，优先返回3级数据，如无则查找2级，再查找1级
  for (let lvl = 3; lvl >= 1; lvl--) {
    const filtered = flatList.filter((item) => item.level === lvl);
    if (filtered.length > 0) {
      return filtered; // 找到符合条件的层级，立即返回
    }
  }

  return []; // 如果数据为空或没有分类，返回空数组
}

export function getQrcodeParams(q) {
  if (!q) return null;

  try {
    const decoded = decodeURIComponent(q);
    const queryString = decoded.split('?')[1];
    if (!queryString) return null;

    const params = {};
    queryString.split('&').forEach((pair) => {
      const [key, value] = pair.split('=');
      if (key) params[key] = decodeURIComponent(value || '');
    });

    return params;
  } catch (e) {
    console.error('解析 q 参数失败:', e);
    return null;
  }
}
