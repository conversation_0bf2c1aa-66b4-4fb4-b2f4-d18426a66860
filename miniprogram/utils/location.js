const app = getApp();

/**
 * 通用定位工具
 * @param {Function} [callback] - 成功回调（可选）
 * @param {boolean} [force=false] - 是否强制刷新定位
 * @returns {Promise<Object|null>} 定位信息
 */
export async function useLocation(callback, force = false) {
  try {
    const location = await app.getLocationInfo(force);
    if (callback) callback(location);
    return location;
  } catch (err) {
    console.error('定位失败:', err);
    if (callback) callback(null);
    return null;
  }
}
