export const USER_TOKEN_KEY = 'accessToken';
export const USER_INFO_KEY = 'userInfo';
export const USER_AGREE_TERMS_PRIVACY_KEY = 'acceptedTermsAndPrivacy';
// ------------------------------------------------------------------------------------------------

// token
export const getToken = () => wx.getStorageSync(USER_TOKEN_KEY);
export const setToken = (token) => wx.setStorageSync(USER_TOKEN_KEY, token);
export const removeToken = () => wx.removeStorageSync(USER_TOKEN_KEY);

// 用户信息
export const getUserInfo = () => wx.getStorageSync(USER_INFO_KEY);
export const setUserInfo = (info) => wx.setStorageSync(USER_INFO_KEY, info);
export const removeUserInfo = () => wx.removeStorageSync(USER_INFO_KEY);

// 是否同意用户协议和隐私政策
export const setAcceptTermsAndPrivacy = () => wx.setStorageSync(USER_AGREE_TERMS_PRIVACY_KEY, true);
export const getAcceptTermsAndPrivacy = () => wx.getStorageSync(USER_AGREE_TERMS_PRIVACY_KEY);
