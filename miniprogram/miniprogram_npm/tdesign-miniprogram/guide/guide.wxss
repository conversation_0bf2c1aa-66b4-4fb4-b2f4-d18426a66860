.t-float-left {
  float: left;
}
.t-float-right {
  float: right;
}
@keyframes tdesign-fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.hotspot-expanded.relative {
  position: relative;
}
.hotspot-expanded::after {
  content: '';
  display: block;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  transform: scale(1.5);
}
.t-guide__reference {
  position: absolute;
  box-shadow: 0 0 0 0 var(--td-guide-reference-mask-color, var(--td-font-gray-2, rgba(0, 0, 0, 0.6))), var(--td-guide-reference-mask-color, var(--td-font-gray-2, rgba(0, 0, 0, 0.6))) 0 0 0 5000px;
  border-radius: var(--td-guide-reference-border-radius, var(--td-radius-default, 12rpx));
  transition: all var(--td-anim-duration-base, 0.2s) var(--td-anim-time-fn-ease-out, cubic-bezier(0, 0, 0.15, 1));
}
.t-guide__reference--nonoverlay {
  box-shadow: none;
  border: var(--td-guide-reference-border, 4rpx solid var(--td-brand-color, var(--td-primary-color-7, #0052d9)));
}
.t-guide__container {
  display: inline-block;
}
.t-guide__container--popover {
  background-color: var(--td-guide-popover-bg-color, var(--td-bg-color-container, var(--td-font-white-1, #ffffff)));
  border: var(--td-guide-popover-border, 2rpx solid var(--td-component-border, var(--td-gray-color-4, #dcdcdc)));
  border-radius: var(--td-guide-popover-border-radius, var(--td-radius-large, 18rpx));
  box-shadow: var(--td-guide-popover-shadow, var(--td-shadow-3, 0 6px 30px 5px rgba(0, 0, 0, 0.05), 0 16px 24px 2px rgba(0, 0, 0, 0.04), 0 8px 10px -5px rgba(0, 0, 0, 0.08)));
  padding: var(--td-guide-popover-padding, var(--td-spacer-2, 32rpx));
  min-width: var(--td-guide-popover-min-width, 480rpx);
  max-width: var(--td-guide-popover-max-width, 540rpx);
}
.t-guide__container--dialog {
  background-color: var(--td-guide-popover-bg-color, var(--td-bg-color-container, var(--td-font-white-1, #ffffff)));
  border-radius: var(--td-guide-dialog-border-radius, var(--td-radius-extra-large, 24rpx));
  padding: var(--td-guide-dialog-padding, var(--td-spacer-3, 48rpx) 0);
  width: var(--td-guide-dialog-width, 622rpx);
}
.t-guide__title--popover {
  text-align: var(--td-guide-popover-title-text-align, left);
  color: var(--td-guide-title-color, var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9))));
  font-size: var(--td-guide-popover-title-font-size, var(--td-font-size-m, 32rpx));
  font-weight: var(--td-guide-title-font-weight, 600);
  line-height: var(--td-guide-popover-title-line-height, 48rpx);
}
.t-guide__title--dialog {
  text-align: var(--td-guide-dialog-title-text-align, center);
  color: var(--td-guide-title-color, var(--td-text-color-primary, var(--td-font-gray-1, rgba(0, 0, 0, 0.9))));
  font-size: var(--td-guide-dialog-title-font-size, 36rpx);
  font-weight: var(--td-guide-title-font-weight, 600);
  line-height: var(--td-guide-dialog-title-line-height, 52rpx);
}
.t-guide__body--popover {
  margin-top: var(--td-guide-popover-body-margin-top, 8rpx);
  text-align: var(--td-guide-popover-body-text-align, left);
  color: var(--td-guide-body-color, var(--td-text-color-secondary, var(--td-font-gray-2, rgba(0, 0, 0, 0.6))));
  font-size: var(--td-guide-popover-body-font-size, var(--td-font-size-base, 28rpx));
  font-weight: var(--td-guide-body-font-weight, 400);
  line-height: var(--td-guide-popover-body-line-height, 44rpx);
}
.t-guide__body--dialog {
  margin-top: var(--td-guide-dialog-body-margin-top, 16rpx);
  text-align: var(--td-guide-dialog-body-text-align, center);
  color: var(--td-guide-body-color, var(--td-text-color-secondary, var(--td-font-gray-2, rgba(0, 0, 0, 0.6))));
  font-size: var(--td-guide-dialog-body-font-size, var(--td-font-size-m, 32rpx));
  font-weight: var(--td-guide-body-font-weight, 400);
  line-height: var(--td-guide-dialog-body-line-height, 48rpx);
}
.t-guide__footer {
  text-align: var(--td-guide-footer-text-align, right);
  margin-top: var(--td-guide-footer-margin-top, var(--td-spacer-3, 48rpx));
}
.t-guide__footer .t-guide__button + .t-guide__button {
  margin-left: var(--td-guide-footer-button-space, var(--td-spacer-1, 24rpx));
}
.t-guide__footer--dialog {
  display: flex;
  padding: var(--td-guide-dialog-footer-button-padding, 0 var(--td-spacer-3, 48rpx));
}
.t-guide__footer--dialog .t-guide__button:last-child {
  flex-grow: 1;
}
