<wxs src="../common/utils.wxs" module="_" />

<t-popup
  wx:if="{{!destroyOnClose || visible}}"
  style="{{_._style([style, customStyle])}}"
  class="class"
  bind:visible-change="visibleChange"
  visible="{{visible}}"
  zIndex="{{zIndex}}"
  usingCustomNavbar="{{usingCustomNavbar}}"
  placement="{{placement == 'right' ? 'right' : 'left'}}"
  showOverlay="{{showOverlay}}"
  closeOnOverlayClick="{{closeOnOverlayClick}}"
>
  <view class="{{classPrefix}}">
    <slot name="title" />
    <view wx:if="{{title}}" class="{{classPrefix}}__title">{{title}}</view>
    <scroll-view class="{{classPrefix}}__sidebar" scroll-y type="list">
      <view
        class="{{classPrefix}}__sidebar-item"
        hover-class="{{classPrefix}}--hover"
        hover-start-time="{{0}}"
        hover-stay-time="{{100}}"
        wx:for="{{items}}"
        wx:item="item"
        wx:key="index"
        data-item="{{item}}"
        data-index="{{index}}"
        bindtap="itemClick"
        aria-role="{{ ariaRole || 'button' }}"
        aria-label="{{item.title}}"
      >
        <view aria-hidden="{{true}}" wx:if="{{item.icon}}" class="{{classPrefix}}__sidebar-item-icon">
          <t-icon name="{{item.icon}}" />
        </view>
        <view class="{{classPrefix}}__sidebar-item-title"> {{item.title}} </view>
      </view>
    </scroll-view>
    <view class="{{classPrefix}}__footer">
      <slot />
      <slot name="footer" />
    </view>
  </view>
</t-popup>
