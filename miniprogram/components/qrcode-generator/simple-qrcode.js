import QRCode from './qrcode-core.js'; // 核心库

export function drawQrcode(ctx, text, size, options = {}) {
  // 优化参数：降低错误容错级别，提高扫描性能
  const errorCorrectionLevel = options.errorCorrectionLevel || 'M'; // 从 'H' 改为 'M'
  const margin = options.margin || 4; // 添加边距，默认4个模块

  const qrcode = new QRCode(-1, errorCorrectionLevel);
  qrcode.addData(text);
  qrcode.make();

  const cells = qrcode.getModuleCount();

  // 计算实际绘制区域，留出边距
  const actualSize = size - margin * 2;
  let tileSize = Math.floor(actualSize / cells); // 使用整数像素，避免模糊

  // 确保tileSize至少为1像素，避免过小的二维码
  tileSize = Math.max(1, tileSize);

  // 重新计算实际使用的尺寸
  const usedSize = cells * tileSize;

  // 计算居中偏移（确保整数像素对齐）
  const offsetX = Math.floor((size - usedSize) / 2);
  const offsetY = Math.floor((size - usedSize) / 2);

  // 清空画布，设置白色背景
  ctx.fillStyle = '#ffffff';
  ctx.fillRect(0, 0, size, size);

  // 设置绘制参数以提高清晰度
  ctx.imageSmoothingEnabled = false; // 禁用图像平滑，保持像素锐利

  // 绘制二维码，使用纯黑色
  ctx.fillStyle = '#000000';
  for (let row = 0; row < cells; row++) {
    for (let col = 0; col < cells; col++) {
      if (qrcode.isDark(row, col)) {
        // 使用整数像素绘制，确保清晰度和像素对齐
        const x = Math.round(offsetX + col * tileSize);
        const y = Math.round(offsetY + row * tileSize);
        ctx.fillRect(x, y, tileSize, tileSize);
      }
    }
  }
}
