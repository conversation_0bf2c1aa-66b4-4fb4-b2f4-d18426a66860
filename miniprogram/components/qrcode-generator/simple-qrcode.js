import QRCode from './qrcode-core.js'; // 核心库

export function drawQrcode(ctx, text, size, options = {}) {
  // 优化参数：降低错误容错级别，提高扫描性能
  const errorCorrectionLevel = options.errorCorrectionLevel || 'M'; // 从 'H' 改为 'M'
  const margin = options.margin || 4; // 添加边距，默认4个模块

  const qrcode = new QRCode(-1, errorCorrectionLevel);
  qrcode.addData(text);
  qrcode.make();

  const cells = qrcode.getModuleCount();

  // 计算实际绘制区域，留出边距
  const actualSize = size - margin * 2;
  const tileSize = Math.floor(actualSize / cells); // 使用整数像素，避免模糊

  // 计算居中偏移
  const offsetX = Math.floor((size - cells * tileSize) / 2);
  const offsetY = Math.floor((size - cells * tileSize) / 2);

  // 清空画布，设置白色背景
  ctx.fillStyle = '#ffffff';
  ctx.fillRect(0, 0, size, size);

  // 绘制二维码，使用黑色
  ctx.fillStyle = '#000000';
  for (let row = 0; row < cells; row++) {
    for (let col = 0; col < cells; col++) {
      if (qrcode.isDark(row, col)) {
        // 使用整数像素绘制，确保清晰度
        const x = offsetX + col * tileSize;
        const y = offsetY + row * tileSize;
        ctx.fillRect(x, y, tileSize, tileSize);
      }
    }
  }
}
