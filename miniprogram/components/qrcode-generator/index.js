import { drawQrcode } from './simple-qrcode.js'; // 我来提供这个轻量二维码生成函数

Component({
  properties: {
    text: {
      type: String,
      value: '',
    },
    size: {
      type: Number,
      value: 200,
    },
    canvasId: {
      type: String,
      value: 'qrcode-canvas',
    },
    // 新增：错误容错级别配置
    errorCorrectionLevel: {
      type: String,
      value: 'M', // L, M, Q, H - M级别平衡了容错性和密度
    },
    // 新增：边距配置
    margin: {
      type: Number,
      value: 4, // 默认4个模块的边距
    },
    // 新增：是否启用点击预览
    enablePreview: {
      type: Boolean,
      value: true,
    },
  },
  data: {
    hasDrawn: false,
    base64Image: '', // 存储生成的base64图片
  },

  observers: {
    text() {
      this.drawQrcode();
    },
  },

  lifetimes: {
    ready() {
      wx.onAppShow(() => {
        console.log('onAppShow');
        if (this.data.hasDrawn) {
          this.drawQrcode(); // 从后台返回时重绘
        }
      });
    },
    detached() {
      console.log('移除监听，避免内存泄漏');
      // 移除监听，避免内存泄漏
      wx.offAppShow();
    },
  },

  methods: {
    drawQrcode() {
      console.log('drawQrcode');
      if (!this.data.text) return;

      const query = this.createSelectorQuery();
      query
        .select(`#${this.data.canvasId}`)
        .fields({ node: true, size: true })
        .exec((res) => {
          const canvas = res[0].node;

          // 设置画布尺寸，使用设备像素比提高清晰度
          const size = this.data.size;
          const dpr = wx.getSystemInfoSync().pixelRatio || 2;

          // 设置实际画布尺寸（高分辨率）
          canvas.width = size * dpr;
          canvas.height = size * dpr;

          // 获取上下文并缩放画布以匹配CSS尺寸
          const ctx = canvas.getContext('2d');
          ctx.scale(dpr, dpr);

          // 使用简易二维码绘制，传入配置选项
          drawQrcode(ctx, this.data.text, size, {
            errorCorrectionLevel: this.data.errorCorrectionLevel,
            margin: this.data.margin,
          });
          // 标记已绘制
          this.setData({ hasDrawn: true });
          // 延迟一点时间，确保绘制完成
          setTimeout(() => {
            const base64 = canvas.toDataURL('image/png');
            // 存储base64图片用于预览
            this.setData({ base64Image: base64 });

            // 触发事件回传给页面
            this.triggerEvent('onGenerated', { base64 });
          }, 100); // 确保绘制完成，100ms 足够
        });
    },

    // 新增：点击预览功能
    onTapPreview() {
      if (!this.data.enablePreview || !this.data.text) return;

      // 生成高分辨率二维码用于预览
      this.generateHighResQrcode();
    },

    // 生成高分辨率二维码用于预览
    generateHighResQrcode() {
      const query = this.createSelectorQuery();
      query
        .select(`#${this.data.canvasId}`)
        .fields({ node: true, size: true })
        .exec((res) => {
          const canvas = res[0].node;

          // 生成更大尺寸的二维码用于预览（提高清晰度）
          const previewSize = Math.max(600, this.data.size * 3); // 至少600px或3倍原尺寸
          const dpr = wx.getSystemInfoSync().pixelRatio || 2;

          // 设置高分辨率画布
          canvas.width = previewSize * dpr;
          canvas.height = previewSize * dpr;

          const ctx = canvas.getContext('2d');
          ctx.scale(dpr, dpr);

          // 绘制高分辨率二维码
          drawQrcode(ctx, this.data.text, previewSize, {
            errorCorrectionLevel: this.data.errorCorrectionLevel,
            margin: this.data.margin,
          });

          // 延迟确保绘制完成
          setTimeout(() => {
            const base64 = canvas.toDataURL('image/png');

            // 将base64转换为临时文件
            const fs = wx.getFileSystemManager();
            const fileName = `qrcode_preview_${Date.now()}.png`;
            const filePath = `${wx.env.USER_DATA_PATH}/${fileName}`;

            try {
              // 去掉base64前缀
              const base64Data = base64.replace(/^data:image\/png;base64,/, '');

              // 写入临时文件
              fs.writeFileSync(filePath, base64Data, 'base64');

              // 预览图片
              wx.previewImage({
                urls: [filePath],
                current: filePath,
              });

              // 恢复原始画布尺寸
              setTimeout(() => {
                this.drawQrcode();
              }, 100);
            } catch (error) {
              console.error('预览二维码失败:', error);
              wx.showToast({
                title: '预览失败',
                icon: 'none',
              });
            }
          }, 100);
        });
    },
  },
});
