# QRCode Generator 组件

一个优化的微信小程序二维码生成组件，支持自定义错误容错级别和边距，提供更好的扫描体验。

## 功能特点

- ✅ 支持自定义错误容错级别（L/M/Q/H）
- ✅ 支持自定义边距设置
- ✅ 使用整数像素绘制，确保清晰度
- ✅ 自动居中对齐
- ✅ 支持从后台返回时重绘
- ✅ 生成 base64 图片数据
- ✅ 支持点击放大预览功能

## 使用方法

### 基础用法

```xml
<qrcode-generator
  text="https://example.com/path?param=value"
  size="200"
/>
```

### 高级配置

```xml
<qrcode-generator
  text="https://api.example.cn/q/redeem?cno=12224689"
  size="300"
  error-correction-level="L"
  margin="6"
  canvas-id="my-qrcode"
  enable-preview="{{ true }}"
  bind:onGenerated="onQrcodeGenerated"
/>
```

## 属性说明

| 属性名                 | 类型    | 默认值          | 说明               |
| ---------------------- | ------- | --------------- | ------------------ |
| text                   | String  | ''              | 二维码内容         |
| size                   | Number  | 200             | 二维码尺寸（像素） |
| canvas-id              | String  | 'qrcode-canvas' | Canvas ID          |
| error-correction-level | String  | 'M'             | 错误容错级别       |
| margin                 | Number  | 4               | 边距（模块数）     |
| enable-preview         | Boolean | true            | 是否启用点击预览   |

### 错误容错级别说明

- **L (Low)**: ~7% 容错率，密度最低，扫描最容易
- **M (Medium)**: ~15% 容错率，平衡密度和容错性
- **Q (Quartile)**: ~25% 容错率，较高容错性
- **H (High)**: ~30% 容错率，最高容错性，密度最高

### 点击预览功能

当 `enable-preview` 设置为 `true` 时，用户可以点击二维码进行全屏预览：

- 支持手势缩放
- 支持长按保存到相册
- 适合需要放大给他人扫描的场景

### 推荐配置

#### 商家扫描场景（如兑换码）

```xml
<qrcode-generator
  text="{{ qrcodeUrl }}"
  error-correction-level="L"
  margin="6"
  enable-preview="{{ true }}"
/>
```

- 使用 L 级别容错，二维码密度最低，扫描最容易
- 增大边距到 6，提供更好的识别边界
- 启用点击预览，方便用户放大给商家扫描

#### 用户分享场景

```xml
<qrcode-generator
  text="{{ shareUrl }}"
  error-correction-level="M"
  margin="4"
  enable-preview="{{ false }}"
/>
```

- 使用 M 级别容错，平衡扫描性能和容错性
- 标准边距，适合大多数场景
- 关闭点击预览，避免误触

#### 复杂内容或小尺寸

```xml
<qrcode-generator
  text="{{ complexUrl }}"
  error-correction-level="Q"
  margin="3"
  size="150"
/>
```

- 使用 Q 级别容错，提高容错性
- 减小边距，为内容留出更多空间

## 事件

### onGenerated

二维码生成完成时触发，返回 base64 图片数据。

```javascript
onQrcodeGenerated(e) {
  const { base64 } = e.detail;
  console.log('二维码生成完成:', base64);
  // 可以保存到相册或进行其他操作
}
```

## 优化说明

### 相比原版本的改进

1. **降低默认错误容错级别**：从 H 改为 M，减少二维码密度
2. **添加边距支持**：避免二维码紧贴边缘，提高识别率
3. **整数像素绘制**：使用 `Math.floor()` 确保像素对齐，避免模糊
4. **居中对齐**：自动计算偏移量，确保二维码居中显示
5. **可配置参数**：支持根据使用场景调整参数

### 性能优化

- 使用整数像素计算，避免浮点数导致的渲染问题
- 优化绘制逻辑，减少不必要的计算
- 支持配置化，避免一刀切的设置

## 注意事项

1. 二维码内容越长，生成的二维码越复杂，建议使用较高的容错级别
2. 在小尺寸下显示时，建议减小边距以留出更多内容空间
3. 商家扫描场景建议使用 L 级别容错，提高扫描成功率
4. 组件会在 `onAppShow` 时自动重绘，确保从后台返回时显示正常
