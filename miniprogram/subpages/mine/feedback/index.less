.page {
  padding: 26rpx 48rpx 120rpx;
  .textarea {
    width: 100%;
    min-height: 350rpx;
    font-weight: 500;
    font-size: 28rpx;
    color: #333;
    line-height: 28rpx;
    padding: 32rpx;
    box-sizing: border-box;
    border-radius: 30rpx;
    background-color: #efefef;
  }
  .upload-images {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin: 32rpx 0;
    .upload-item {
      position: relative;
      width: 188rpx;
      height: 188rpx;
      border-radius: 14rpx;
      margin-right: 20rpx;
      background-color: #f2f2f2;
      margin-bottom: 20rpx;
      .img {
        width: 100%;
        height: 100%;
        border-radius: 14rpx;
      }
      .ic-delete {
        position: absolute;
        top: -10rpx;
        right: -10rpx;
        width: 40rpx;
        height: 40rpx;
      }
      // &:nth-child(even) {
      //   margin-right: 0;
      // }
    }
    .btn-add {
      .ic {
        width: 80rpx;
        height: 80rpx;
      }
    }
  }
  .btn-submit {
    position: fixed;
    bottom: 120rpx;
    left: 0;
    right: 0;
    width: 654rpx;
    height: 96rpx;
    line-height: 96rpx;
    text-align: center;
    border-radius: 48rpx;
    font-size: 32rpx;
    color: #333;
    margin: 0 auto 48rpx;
    background: linear-gradient(to right, #ffff82, #bafd8f);
  }
}
