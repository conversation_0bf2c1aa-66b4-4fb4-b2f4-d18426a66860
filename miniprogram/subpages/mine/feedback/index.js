Page({
  data: {
    content: '',
    uploadImages: [
      'http://mmbiz.qpic.cn/sz_mmbiz_jpg/GEWVeJPFkSEz7tgvlaTtv2MYO01RZr0yNgtbEZJzcbRl0deOWmSbX0UfRHPt78UCOxPIVYnhAiaJVib40SviaV1Vw/0?wx_fmt=jpeg',
    ],
  },
  // onLoad() {},
  onTextareaInput(e) {
    this.setData({
      content: e.detail.value,
    });
  },
  onAddImage() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      // maxDuration: 30,
      camera: 'back',
      success: (res) => {
        console.log('res:', res);
        console.log('this.data:', this.data);
        const { uploadImages } = this.data;

        uploadImages.push(res.tempFiles[0].tempFilePath);
        this.setData({
          uploadImages,
        });
      },
    });
  },
  onDeleteImage(e) {
    const { index } = e.currentTarget.dataset;
    const { uploadImages } = this.data;
    uploadImages.splice(index, 1);

    this.setData({
      uploadImages,
    });
  },
  onSubmit() {
    const { content } = this.data;
    if (content) {
      wx.showToast({
        title: '反馈成功',
        icon: 'success',
        duration: 2000,
      });

      setTimeout(() => {
        wx.navigateBack({
          delta: 1,
        });
      }, 2000);
    }
  },
});
