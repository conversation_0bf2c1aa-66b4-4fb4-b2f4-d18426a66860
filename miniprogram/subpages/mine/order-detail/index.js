import { orderDetail } from '@/api/index';
import { encryptPhoneNumber } from '@/utils/util';
const app = getApp();

Component({
  properties: {
    id: {
      type: String,
      value: '',
    },
  },
  data: {
    ic_arrow: '/assets/image/icon/arrow-right.png',
    order: {},
    showUserMobileText: '',
    // 订单状态：0-未付款；1-已付款；2-已取消；3-已退款, 详见枚举 OmsOrderStatus
  },
  pageLifetimes: {
    show() {
      this.init();
    },
  },
  methods: {
    async init() {
      this.getOrderDetail();
    },
    async getOrderDetail() {
      wx.showLoading({ title: '加载中' });

      const res = await orderDetail({ id: this.data.id });
      const { success, data } = res;
      const user = app.globalData.userInfo;

      if (success && data) {
        const totalPrice = Number(data.retailPrice * data.quantity).toFixed(2);
        const order = {
          ...data,
          totalPrice,
          discountPrice: (totalPrice - data.activityPrice).toFixed(2),
        };
        this.setData({ order, showUserMobileText: encryptPhoneNumber(user.mobile) });
      }
      wx.hideLoading({ noConflict: true });
    },
    onTapNotice() {
      // open dialog
    },
    onRefund() {
      wx.navigateTo({
        url: '/subpages/mine/order-refund/index?id=' + this.data.order.id,
      });
    },
  },
});
