page {
  background-color: #f2f2f2;
}
.content-wrap {
  padding: 30rpx 24rpx 200rpx;
  .order-box {
    padding: 32rpx 24rpx;
    margin-bottom: 24rpx;
    border-radius: 28rpx;
    background-color: #fff;
    .top-block {
      display: flex;
      align-items: center;
      .img {
        width: 104rpx;
        height: 104rpx;
        border-radius: 14rpx;
      }
      .right-block {
        margin-left: 18rpx;
        .name {
          height: 28rpx;
          font-weight: 500;
          font-size: 28rpx;
          color: #333;
          line-height: 28rpx;
        }
        .tip {
          margin-top: 16rpx;
          height: 20rpx;
          font-weight: 400;
          font-size: 20rpx;
          color: #666;
          line-height: 20rpx;
          .title {
            font-weight: 500;
          }
          .ic {
            width: 16rpx;
            height: 16rpx;
            margin-left: 12rpx;
          }
        }
      }
    }
    .center-block {
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-top: 1rpx solid #ccc;
      border-bottom: 1rpx solid #ccc;
      padding: 30rpx 0;
      margin: 24rpx 0;
      .label {
        height: 24rpx;
        font-size: 24rpx;
        color: #333;
        line-height: 24rpx;
        .num {
          color: #999;
        }
      }
      .value {
        .price {
          display: flex;
          align-items: flex-end;
          color: #333;
          .symbol {
            font-size: 24rpx;
          }
          .val {
            margin-left: 2rpx;
            font-size: 32rpx;
          }
        }
      }
    }
    .bottom-block {
      .item {
        display: flex;
        align-items: center;
        justify-content: space-between;

        margin-bottom: 32rpx;
        .label {
          height: 24rpx;
          font-weight: 500;
          font-size: 24rpx;
          color: #333;
          line-height: 24rpx;
        }
        .value {
          height: 24rpx;
          font-size: 24rpx;
          color: #fa5151;
          line-height: 24rpx;
        }
      }
      .item2 {
        .label {
          color: #333;
          margin-right: 14rpx;
        }
        .val {
          color: #fa5151;
        }
      }
    }
  }

  .info-box {
    padding: 32rpx 24rpx 1rpx;
    border-radius: 28rpx;
    background-color: #fff;
    .title {
      height: 28rpx;
      font-weight: 500;
      font-size: 28rpx;
      color: #000;
      line-height: 28rpx;
      margin-bottom: 32rpx;
    }
    .item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 28rpx;
      line-height: 28rpx;
      margin-bottom: 32rpx;
      .label {
        color: #999;
      }
      .value {
        color: #000;
      }
    }
  }
}
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 140rpx;
  padding: 0 36rpx;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  background-color: #fff;
  box-shadow: 0rpx -12rpx 20rpx 0rpx rgba(0, 0, 0, 0.05);
  .left {
    .price {
      color: #fa5151;

      .symbol {
        font-size: 24rpx;
        line-height: 24rpx;
      }
      .val {
        height: 40rpx;
        font-family: Roboto, Roboto;
        font-weight: bold;
        font-size: 40rpx;
        line-height: 40rpx;
      }
    }
    .info {
      height: 20rpx;
      font-size: 20rpx;
      color: #666;
      line-height: 20rpx;
      margin-top: 16rpx;
    }
  }
  .left2 {
    font-size: 28rpx;
    color: #333333;
    text-align: left;
  }
  .right {
    .btn {
      width: 200rpx;
      height: 72rpx;
      line-height: 72rpx;
      font-size: 28rpx;
      text-align: center;
      color: #000;
      border-radius: 45rpx;
      background-color: #fff;
      border: 1rpx solid #efefef;
    }
    .btn-refund {
      height: 40rpx;
      font-weight: 500;
      font-size: 40rpx;
      color: #333333;
      line-height: 40rpx;
      text-align: right;
    }
  }
}
