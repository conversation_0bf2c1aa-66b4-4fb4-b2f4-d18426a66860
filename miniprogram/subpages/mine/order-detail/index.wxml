<t-navbar title="订单详情" left-arrow />
<scroll-view class="scrollarea" scroll-y>
  <view class="content-wrap">
    <!-- 订单信息 -->
    <view class="order-box">
      <view class="top-block">
        <custom-image class="img" src="{{ order.imageUrl }}" />
        <view class="right-block">
          <view class="name">{{ order.title }} ({{ order.quantity }}份)</view>
          <view class="flex items-center tip">
            <text class="title">限制</text><text>：{{ order.usedLimitsText }}</text>
          </view>
          <view class="flex items-center tip" bindtap="onTapNotice">
            <view class="flex items-center">
              <text class="title">须知</text> <text>：{{ order.usedTimeLimitsText }}</text>
            </view>
            <!-- <image class="ic" src="{{ ic_arrow }}" /> -->
          </view>
        </view>
      </view>

      <view class="center-block">
        <view class="flex items-center label">
          <text>商品总价</text>
          <text class="num"> (共{{ order.quantity }}份)</text>
        </view>
        <view class="value">
          <view class="price">
            <view class="symbol">¥</view>
            <view class="val">{{ order.totalPrice }}</view>
          </view>
        </view>
      </view>
      <view class="bottom-block">
        <view class="item">
          <view class="label">优惠合计</view>
          <view class="flex items-center value">
            <text style="color: #333">共：</text>
            <text>-¥{{ order.discountPrice }}</text>
          </view>
        </view>
        <view class="item">
          <view class="label">有券共省</view>
          <view class="value">-¥{{ order.discountPrice }}</view>
        </view>
        <view class="flex items-center justify-end item2">
          <text class="label">实际支付：</text>
          <text class="val">¥ {{ order.activityPrice }}</text>
        </view>
      </view>
    </view>
    <!-- TODO：该处状态判断需要处理 -->
    <!-- 近期核销 -->
    <view wx:if="{{ order.status == 2 }}" class="info-box">
      <view class="title">近期核销</view>
      <view class="item">
        <view class="label">核销内容</view>
        <view class="value">{{ order.title }} ({{ order.quantity }}份)</view>
      </view>
      <view class="item">
        <view class="label">桌号</view>
        <view class="value">{{ order.tableNo }}</view>
      </view>
      <view class="item">
        <view class="label">人数</view>
        <view class="value">{{ order.consumerCount }}</view>
      </view>
      <view class="item">
        <view class="label">商户全称</view>
        <view class="value">{{ order.shopName }}</view>
      </view>
      <view class="item">
        <view class="label">交易时间</view>
        <view class="value">{{ order.payTime }}</view>
      </view>
      <view class="item">
        <view class="label">手机号码</view>
        <view class="value">{{ order.userMobile }}</view>
      </view>
      <view class="item">
        <view class="label">订单号</view>
        <view class="value">{{ order.orderNo }}</view>
      </view>
    </view>

    <!-- 商户信息 -->
    <view class="info-box">
      <view class="item">
        <view class="label">商户全称</view>
        <view class="value">{{ order.shopName || '--' }}</view>
      </view>
      <view class="item">
        <view class="label">交易时间</view>
        <view class="value">{{ order.payTime || '--' }}</view>
      </view>
      <view class="item">
        <view class="label">手机号码</view>
        <view class="value">{{ showUserMobileText }}</view>
      </view>
      <view class="item">
        <view class="label">订单号</view>
        <view class="value">{{ order.orderNo }}</view>
      </view>
    </view>

    <!-- 购买须知 -->
    <view class="info-box">
      <view class="title">购买须知</view>
      <view class="item">
        <view class="label">有效期</view>
        <view class="value">解锁后{{ order.expireDays || 0 }}天有效</view>
      </view>
    </view>
  </view>

  <view class="flex items-center justify-between footer">
    <view wx:if="{{ order.status === 3 }}" class="left2">
      退款金额：¥ {{ order.activityPrice }}
    </view>
    <view wx:else class="left">
      <view class="flex items-end price">
        <view class="symbol">¥</view>
        <view class="val">{{ order.activityPrice }}</view>
      </view>
      <view class="info"> 共{{ order.quantity }}份 已优惠¥ {{ order.discountPrice }} </view>
      <!-- <view class="btn">联系客服</view> -->
    </view>
    <view class="flex items-center justify-end right">
      <!-- 用户参加的活动状态：1-未使用;2-使用中;3-使用完成;9-已退款 -->
      <view wx:if="{{ order.userActivityStatus === 1 }}" class="btn" bindtap="onRefund">
        申请退款
      </view>
      <view wx:if="{{ order.userActivityStatus === 9 }}" class="btn-refund">退款成功</view>
    </view>
  </view>
</scroll-view>
