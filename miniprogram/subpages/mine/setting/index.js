import { removeToken, removeUserInfo } from '@/utils/auth';

const app = getApp();

Component({
  data: {
    ic_arrow: '/assets/image/icon/arrow-right.png',
  },
  methods: {
    onTapPolicy() {
      wx.navigateTo({
        url: '/subpages/mine/policy/index',
      });
    },
    onTapAgreement() {
      wx.navigateTo({
        url: '/subpages/mine/agreement/index',
      });
    },
    loginOut() {
      wx.showModal({
        content: '确认退出登录吗？',
        showCancel: true,
        cancelText: '取消',
        cancelColor: '#000000',
        confirmText: '确定',
        confirmColor: '#54d601',
        success: (result) => {
          if (result.confirm) {
            wx.showLoading({ title: '退出中...' });
            removeToken();
            removeUserInfo();
            app.checkUserPromise = null;
            app.loginInProgress = false;
            app.isRedirecting = false;
            app.globalData.isLoggedIn = false;
            app.globalData.userInfo = {
              openId: app.globalData.userInfo.openId,
              unionId: app.globalData.userInfo.unionId,
            };

            setTimeout(() => {
              wx.hideLoading({ noConflict: true });

              wx.reLaunch({ url: '/subpages/auth/login/index?showHome=1' });
            }, 500);
          }
        },
        fail: () => {},
        complete: () => {},
      });
    },
  },
});
