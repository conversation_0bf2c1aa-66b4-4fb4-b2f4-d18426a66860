<t-navbar left-arrow />
<view class="page">
  <view class="list">
    <view class="item">
      <view class="label">头像</view>
      <view class="right-wrap">
        <view class="value-wrap">
          <custom-image class="img" src="{{ userInfo.headImg }}" radius="50%" />
          <image class="ic-right" src="{{ ic_arrow }}" />
        </view>
        <button
          class="avatar-wrapper"
          open-type="chooseAvatar"
          bind:chooseavatar="onChooseAvatar"
        ></button>
      </view>
    </view>
    <view class="item" bindtap="onTapName">
      <view class="label">姓名</view>
      <view class="value-wrap">
        <view class="value">{{ userInfo.fullName || userInfo.nickname || '--' }}</view>
        <image class="ic-right" src="{{ ic_arrow }}" />
      </view>
    </view>
    <view class="item" bindtap="onTapMobile">
      <view class="label">手机</view>
      <view class="value-wrap">
        <view class="value">{{ userInfo.showMobile }}</view>
        <image class="ic-right" src="{{ ic_arrow }}" />
      </view>
    </view>
    <view class="item">
      <view class="label">设置</view>
      <view class="value-wrap" bindtap="onTapSetting">
        <view class="value">隐私政策/协议</view>
        <image class="ic-right" src="{{ ic_arrow }}" />
      </view>
    </view>
  </view>
</view>

<t-popup
  visible="{{ showEditNameVisible }}"
  bind:visible-change="onNameVisibleChange"
  placement="bottom"
  close-on-overlay-click
>
  <view class="edit-store-popup">
    <view class="ic-close" data-key="showEditNameVisible" bindtap="hidePopup">
      <image class="ic" src="/assets/image/icon/close.svg" />
    </view>
    <view class="title">修改姓名</view>
    <input
      type="nickname"
      class="form-input"
      placeholder="请输入姓名"
      data-key="fullName"
      value="{{ popupInfo.fullName || popupInfo.nickname }}"
      bind:input="onInputPopup"
    />
    <view class="btn-submit" bindtap="onSaveName">保存</view>
  </view>
</t-popup>

<t-popup
  visible="{{ showEditMobileVisible }}"
  bind:visible-change="onMobileVisibleChange"
  placement="bottom"
  close-on-overlay-click
>
  <view class="edit-store-popup">
    <view class="ic-close" data-key="showEditMobileVisible" bindtap="hidePopup">
      <image class="ic" src="/assets/image/icon/close.svg" />
    </view>
    <view class="title">修改手机号码</view>
    <input
      type="number"
      maxlength="{{ 11 }}"
      class="form-input"
      placeholder="请输入手机号"
      data-key="mobile"
      value="{{ popupInfo.mobile }}"
      bind:input="onInputPopup"
    />
    <view class="form-input-wrap">
      <input
        type="number"
        class="form-input form-input2"
        placeholder="请输入验证码"
        data-key="code"
        value="{{ popupInfo.code }}"
        bind:input="onInputPopup"
      />
      <view class="code-text">
        <text wx:if="{{ !countdown }}" class="form-code-text" bindtap="onGetCode">获取验证码</text>
        <text wx:else class="form-code-countdown">{{ countdown }}s</text>
      </view>
    </view>
    <view class="btn-submit" bindtap="onSaveMobile">保存</view>
  </view>
</t-popup>
