import { userUpdate, sendVerifyCode2, changeMobile, uploadImage } from '@/api/index';
import { encryptPhoneNumber } from '@/utils/util';
import { DEFAULT_USER_AVATAR } from '@/utils/constant';
import { isMobile } from '@/utils/validate';

const DEFAULT_COUNTDOWN = 60;

const app = getApp();

Component({
  data: {
    ic_arrow: '/assets/image/icon/arrow-right.png',
    userInfo: {},
    popupInfo: {},
    showEditNameVisible: false,
    showEditMobileVisible: false,
    countdown: 0,
    timer: null,
  },
  lifetimes: {
    attached() {
      this.init();
    },
    detached() {
      const { timer } = this.data;
      if (timer) {
        clearInterval(timer);
        this.setData({ timer: null });
      }
    },
  },
  methods: {
    async init() {
      const user = app.globalData.userInfo;

      if (user) {
        const userInfo = {
          ...user,
          headImg: user.headImg || user.avatarUrl || user.avatarName || DEFAULT_USER_AVATAR,
          showMobile: encryptPhoneNumber(user.mobile),
        };

        this.setData({
          userInfo,
          popupInfo: userInfo,
        });
      }
    },
    onTextareaInput(e) {
      this.setData({
        content: e.detail.value,
      });
    },
    onTapName() {
      this.setData({ showEditNameVisible: true });
    },
    onTapMobile() {
      this.setData({ showEditMobileVisible: true });
    },
    onNameVisibleChange(e) {
      this.setData({ showEditNameVisible: e.detail.visible });
    },
    onMobileVisibleChange(e) {
      this.setData({ showEditMobileVisible: e.detail.visible });
    },
    hidePopup(e) {
      const { key } = e.currentTarget.dataset;
      this.setData({ [key]: false });
    },
    onTapSetting() {
      wx.navigateTo({
        url: '/subpages/mine/setting/index',
      });
    },
    async handleSaveUserInfo(params, cbOK) {
      wx.showLoading({ title: '提交中...' });
      const res = await userUpdate(params);
      wx.hideLoading({ noConflict: true });

      if (res.success) {
        wx.showToast({ title: '修改成功！', icon: 'none' });
        cbOK();
      }
    },
    onInputPopup(e) {
      const { key } = e.currentTarget.dataset;
      const { value } = e.detail;

      this.setData({ [`popupInfo.${key}`]: value });
    },
    onSaveName() {
      const { fullName } = this.data.popupInfo;
      this.handleSaveUserInfo({ fullName }, () => {
        this.setData({
          showEditNameVisible: false,
          userInfo: { ...this.data.userInfo, fullName },
        });
        app.globalData.userInfo = {
          ...app.globalData.userInfo,
          fullName,
        };
      });
    },
    // -----------------------------------------------------------------
    // 倒计时
    initCountdown() {
      const { timer } = this.data;
      if (timer) {
        clearTimeout(timer);
      }

      const endTime = Date.now() + this.data.countdown * 1000;

      const runCountdown = () => {
        const remaining = Math.round((endTime - Date.now()) / 1000);

        if (remaining <= 0) {
          this.setData({ countdown: 0 });
          return;
        }

        this.setData({
          countdown: remaining,
          timer: setTimeout(runCountdown, 1000),
        });
      };

      runCountdown();
    },
    async onGetCode() {
      const { mobile, timer } = this.data.popupInfo;

      if (!mobile || !isMobile(mobile)) {
        wx.showToast({
          title: '请输入正确的手机号',
          icon: 'none',
        });
        return;
      }

      // Start countdown
      this.setData({
        countdown: DEFAULT_COUNTDOWN,
      });

      wx.showLoading({ title: '发送中...' });

      const res = await sendVerifyCode2({ mobile });
      if (res.success) {
        this.initCountdown();
        wx.showToast({ title: '验证码已发送', icon: 'none' });
      } else {
        // 重置倒计时
        this.setData({ countdown: 0 });
        if (timer) {
          clearInterval(timer);
        }
      }
    },
    async onSaveMobile() {
      wx.showLoading({ title: '提交中...' });
      const { mobile, code } = this.data.popupInfo;
      if (!mobile) {
        wx.showToast({ title: '请输入手机号', icon: 'none' });

        return;
      }
      if (!isMobile(mobile)) {
        wx.showToast({ title: '请输入正确的手机号', icon: 'none' });
        return;
      }

      if (!code) {
        wx.showToast({ title: '请输入验证码', icon: 'none' });
        return;
      }
      const res = await changeMobile({ mobile, code });

      if (res.success) {
        wx.showToast({ title: '保存成功' });
        const user = {
          ...this.data.userInfo,
          mobile,
          showMobile: encryptPhoneNumber(mobile),
        };
        app.globalData.userInfo = user;
        this.setData({ userInfo: user, popupInfo: user, showEditMobileVisible: false });
      }
      wx.hideLoading({ noConflict: true });
    },
    async onUpload(filePath) {
      wx.showLoading({ title: '上传中...' });
      const res = await uploadImage({ filePath });

      if (res.success) {
        const headImg = res.data.url || '';
        this.handleSaveUserInfo({ headImg }, () => {
          this.setData({
            userInfo: { ...this.data.userInfo, headImg },
          });
          app.globalData.userInfo = {
            ...app.globalData.userInfo,
            headImg,
          };
        });
      }
      wx.hideLoading({ noConflict: true });
    },
    onChooseAvatar(e) {
      this.onUpload(e.detail.avatarUrl);
    },
  },
});
