import { orderRefund, uploadImage } from '@/api/index';

Component({
  properties: {
    id: {
      type: String,
      value: '',
    },
  },
  data: {
    description: '',
    imageList: [],
  },
  methods: {
    onAddImage() {
      wx.chooseMedia({
        count: 1,
        mediaType: ['image'],
        sourceType: ['album', 'camera'],
        camera: 'back',
        success: (res) => {
          const val = res.tempFiles[0]?.tempFilePath || '';

          this.onUpload(val);
        },
      });
    },
    async onUpload(filePath) {
      const res = await uploadImage({ filePath });
      const { success, data } = res;

      if (success && data.url) {
        const val = data.url;

        this.setData({ imageList: [...this.data.imageList, val] });
      }
    },
    onDeleteImage(e) {
      const { index } = e.currentTarget.dataset;
      const { imageList } = this.data;
      imageList.splice(index, 1);
      this.setData({ imageList });
    },
    onTextareaInput(e) {
      this.setData({ description: e.detail.value });
    },
    async onSubmitRefund() {
      wx.showLoading({
        title: '加载中',
      });

      const { id, description, imageList } = this.data;
      const res = await orderRefund({ id, description, images: imageList.join(',') });
      if (res.success) {
        wx.showToast({
          title: '申请成功！',
          icon: 'none',
        });
        wx.navigateBack();
      }
      wx.hideLoading({ noConflict: true });
    },
  },
});
