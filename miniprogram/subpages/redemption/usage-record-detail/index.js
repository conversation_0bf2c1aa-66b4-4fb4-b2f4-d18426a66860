import { usedCouponDetail } from '@/api/index';

Component({
  properties: {
    id: {
      type: String,
      value: '',
    },
  },
  data: {
    info: {},
    itemList: [
      { label: '桌号', key: 'tableNo', value: '', suffix: '号' },
      { label: '人数', key: 'consumerCount', value: '', suffix: '人' },
      { label: '商户全称', key: 'shopName', value: '', suffix: '' },
      { label: '核销时间', label2: '过期时间', key: 'usedTime', value: '', suffix: '' },
      { label: '手机号码', key: 'mobile', value: '', suffix: '' },
      // TODO：缺少字段，以及相关需求
      // { label: '交易快照', key: '', value: '', suffix: '' },
      { label: '订单号', key: 'orderNo', value: '', suffix: '' },
    ],
  },
  lifetimes: {
    attached() {
      this.init();
    },
  },
  methods: {
    async init() {
      this.getInfo();
    },
    async getInfo() {
      const { id } = this.data;

      const res = await usedCouponDetail({ id });

      if (res.success && res.data) {
        const info = res.data;

        const itemList = this.data.itemList.map((item) => ({
          ...item,
          label: item.key === 'usedTime' && info.usedStatus == 2 ? item.label2 : item.label,
          value: info[item.key] !== undefined ? info[item.key] + item.suffix : item.value,
        }));

        this.setData({ info, itemList });
      }
    },
    onShowRule() {
      this.setData({ showRulePopup: true });
    },
    onCloseRulePopup() {
      this.setData({ showRulePopup: false });
    },
    onVisibleChange(e) {
      this.setData({ showRulePopup: e.detail.visible });
    },
  },
});
