<t-navbar title="" left-arrow />

<!-- prettier-ignore -->
<view class="page">
  <view class="data-board">
    <view class="label label-{{ info.usedStatus }}">{{ info.usedStatusText || '--' }}</view>
    <view class="value">已于{{ info.usedTime || '--' }}到店消费</view>
  </view>

  <view class="flex items-center justify-between coupon-item">
    <view class="flex items-center">
      <custom-image class="img" src="{{ info.imageUrl }}" />
      <view class="flex flex-col info">
        <text class="truncate name">{{ info.title || '--' }}（1{{ info.unitsText }}）</text>
        <text class="truncate desc">限制：{{ info.usedLimitsText || '--' }}</text>
        <text class="truncate price" bindtap="onShowRule">须知：{{ info.usedTimeLimitsText || '--' }} ></text>
      </view>
    </view>
  </view>

  <view class="list-title">信息详细</view>
  <view class="list-items">
    <view
      wx:for="{{ itemList }}"
      wx:key="index"
      class="flex items-center justify-between item"
      data-item="{{ item }}"
      bindtap="onTapItem"
    >
      <view class="label">{{ item.label }}</view>
      <view class="value">{{ item.value || '无' }}</view>
    </view>
  </view>
</view>

<t-popup
  visible="{{ showRulePopup }}"
  bind:visible-change="onVisibleChange"
  placement="center"
  close-on-overlay-click
>
  <view class="rule-container2">
    <view class="title">购买须知</view>
    <view class="sub-title">使用限制</view>
    <view class="sub-content">{{ info.usedLimitsText }}</view>
    <view class="sub-title">使用时间</view>
    <view class="sub-content">{{ info.usedTimeLimitsText }}</view>
    <view class="sub-title">使用规则</view>
    <text class="sub-content" space="ensp">{{ info.usedRules }}</text>
    <t-icon
      t-class="close-btn"
      name="close-circle"
      size="64rpx"
      color="#fff"
      bind:tap="onCloseRulePopup"
    />
  </view>
</t-popup>
