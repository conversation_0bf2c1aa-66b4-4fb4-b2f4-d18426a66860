.page {
  padding: 0 48rpx 120rpx;

  .data-board {
    padding: 32rpx 0 48rpx;
    .label {
      height: 40rpx;
      font-weight: 500;
      font-size: 40rpx;
      color: #333333;
      line-height: 40rpx;
      margin-bottom: 16rpx;
    }
    .label-2 {
      color: #999999;
    }
    .value {
      height: 22rpx;
      font-size: 22rpx;
      color: #999999;
      line-height: 22rpx;
    }
  }

  .coupon-item {
    padding: 48rpx 0;
    background-color: #ffffff;
    border-radius: 28rpx;
    border-top: 1rpx solid rgba(144, 152, 169, 0.35);
    border-bottom: 1rpx solid rgba(144, 152, 169, 0.35);
    .img {
      width: 104rpx;
      height: 104rpx;
      background-color: #d8d8d8;
      border-radius: 14rpx;
    }
    .info {
      flex: 1;
      margin-left: 16rpx;
      .name {
        height: 28rpx;
        font-weight: 500;
        font-size: 28rpx;
        color: #333;
        line-height: 28rpx;
      }
      .desc {
        height: 20rpx;
        font-weight: 400;
        font-size: 20rpx;
        color: #999;
        line-height: 20rpx;
        margin-top: 16rpx;
      }
      .price {
        height: 20rpx;
        font-size: 20rpx;
        color: #999;
        line-height: 20rpx;
        margin-top: 16rpx;
      }
    }
    .state {
      min-width: 180rpx;
      height: 24rpx;
      font-size: 24rpx;
      color: #999;
      line-height: 24rpx;
      text-align: right;
    }
    .status-1 {
      color: #fa5151;
    }
    .status-2 {
      color: @brand-color;
    }
  }

  .list-title {
    height: 28rpx;
    font-weight: 500;
    font-size: 28rpx;
    color: #000000;
    line-height: 28rpx;
    margin-top: 46rpx;
    margin-bottom: 32rpx;
  }
  .list-items {
    .item {
      // padding: 32rpx 0;
      margin-bottom: 32rpx;
      .label {
        height: 28rpx;
        font-size: 28rpx;
        color: #999999;
        line-height: 28rpx;
      }
      .value {
        height: 28rpx;
        font-size: 28rpx;
        color: #000000;
        line-height: 28rpx;
      }
    }
  }
}

.rule-container2 {
  position: relative;
  width: 718rpx;
  background: #ffffff;
  border-radius: 28rpx;
  padding: 80rpx 48rpx 32rpx;
  box-sizing: border-box;
  .title {
    height: 44rpx;
    font-weight: 500;
    font-size: 44rpx;
    color: #333333;
    line-height: 44rpx;
    text-align: center;
    margin-bottom: 48rpx;
  }
  .sub-title {
    height: 28rpx;
    font-weight: 500;
    font-size: 28rpx;
    color: #333333;
    line-height: 28rpx;
    margin-bottom: 16rpx;
  }
  .sub-content {
    font-size: 28rpx;
    color: #999999;
    line-height: 42rpx;
    margin-bottom: 48rpx;
  }
  .close-btn {
    position: absolute;
    left: 50%;
    margin-left: -32rpx;
    bottom: calc(-1 * (48rpx + 84rpx));
  }
}
