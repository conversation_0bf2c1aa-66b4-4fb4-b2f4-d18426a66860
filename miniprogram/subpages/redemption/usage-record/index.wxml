<t-navbar title="累计使用" left-arrow />

<scroll-view
  scroll-y
  style="flex: 1"
  show-scrollbar="{{ false }}"
  bindscrolltolower="onReachBottom"
>
  <view class="page">
    <view class="data-board">
      <view class="item">
        <view class="label">{{ totalNumText }}</view>
        <view class="value">张</view>
      </view>
    </view>

    <view class="flex items-center justify-center filter-box">
      <view class="flex items-center justify-center item" bindtap="onTapStatus">
        <text class="text">{{ usedStatusText || '全部' }}</text>
        <t-icon class="ic" name="caret-down-small" size="30rpx" color="#999" />
      </view>
    </view>

    <view class="shop-list">
      <view
        wx:for="{{ list }}"
        wx:key="id"
        class="flex items-center justify-between shop-item"
        data-item="{{ item }}"
        bindtap="onTapItem"
      >
        <view class="flex flex-col left">
          <text class="truncate name">{{ item.title }}（价值¥{{ item.retailPrice }}）</text>
          <text class="time">{{ item.shopName }}</text>
        </view>
        <view class="flex items-center right">
          <view class="flex flex-col block">
            <text class="user user-{{ item.usedStatus }}">{{ item.usedStatusText || '--' }}</text>
            <text class="order">{{ item.usedTime }}</text>
          </view>
          <image class="ic" src="/assets/image/icon/arrow-right.png" />
        </view>
      </view>
      <t-empty wx:if="{{ list.length === 0 }}" description="暂无数据" />
    </view>
  </view>
</scroll-view>
