import { usedCouponList } from '@/api/index';

const statusData = [
  { label: '全部', value: null },
  { label: '已使用', value: 1 },
  { label: '已过期', value: 2 },
];

const statusOptions = statusData.map((e) => e.label);

function formatNumber(n) {
  return n.toString().padStart(2, '0');
}

Component({
  properties: {
    activityId: {
      type: String,
      value: '1',
    },
  },
  data: {
    list: [],
    // 核销(使用)状态：1-已使用;2-已过期;	
    usedStatus: '',
    usedStatusText: '',
    totalNumText: '0'
  },
  lifetimes: {
    attached() {
      this.init();
    },
  },
  methods: {
    init() {
      this.getList();
    },
    async getList(params = {}) {
      wx.showLoading({ title: '加载中...' })
      const { activityId, usedStatus } = this.data;
      const res = await usedCouponList({
        activityId,
        usedStatus,
        ...params,
      });

      if (res.success && res.data) {
        this.setData({
          list: res.data,
          totalNumText: formatNumber(res.data.length || 0)
        });
      }
      setTimeout(() => wx.hideLoading(), 200)
    },
    handleChangeStatus(e) {
      const params = {
        usedStatus: e.value,
      };

      this.setData({
        ...params,
        usedStatusText: e.label,
      });

      this.getList(params);
    },
    onTapStatus() {
      wx.showActionSheet({
        itemList: statusOptions,
        success: (res) => {
          console.log(res);
          this.handleChangeStatus(statusData[res.tapIndex]);
        },
        fail(res) {
          console.log(res.errMsg);
        },
      });
    },
    onTapItem(e) {
      const { item } = e.currentTarget.dataset;

      wx.navigateTo({
        url: `/subpages/redemption/usage-record-detail/index?id=${item.id}`,
      });
    },
  },
});
