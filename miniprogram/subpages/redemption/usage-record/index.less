.page {
  padding-bottom: 150rpx;
  .data-board {
    flex: 1;
    padding: 48rpx;
    border-bottom: 1rpx solid rgba(144, 152, 169, 0.35);
    .item {
      display: flex;
      align-items: flex-end;
      justify-content: center;
      color: #333;
      .label {
        font-size: 68rpx;
        line-height: 1;
        font-family: <PERSON><PERSON>, Roboto;
        font-weight: bold;
        color: #000000;
      }
      .value {
        font-size: 28rpx;
        line-height: 1;
        color: #333333;
        margin-left: 8rpx;
        padding-bottom: 5rpx;
      }
    }
    // .item {
    //   display: flex;
    //   align-items: flex-end;
    //   justify-content: center;
    //   .label {
    //     // height: 68rpx;
    //     font-family: Robot<PERSON>, Roboto;
    //     font-weight: bold;
    //     font-size: 68rpx;
    //     color: #000000;
    //     // line-height: 68rpx;
    //   }
    //   .value {
    //     // height: 28rpx;
    //     font-size: 28rpx;
    //     color: #333333;
    //     line-height: 28rpx;
    //     margin-left: 8rpx;
    //   }
    // }
  }

  .filter-box {
    padding: 24rpx;
    .item {
      width: 200rpx;
      height: 64rpx;
      border-radius: 4rpx;
      text-align: center;
      border: 2rpx solid rgba(153, 153, 153, 0.35);
    }
  }

  .shop-list {
    padding: 0 48rpx;
    .shop-item {
      height: 124rpx;
      .left {
        .name {
          width: 380rpx;
          font-size: 28rpx;
          color: #000000;
          line-height: 30rpx;
        }
        .time {
          font-size: 22rpx;
          color: #999999;
          line-height: 24rpx;
          margin-top: 24rpx;
        }
      }

      .right {
        .block {
          .user {
            height: 32rpx;
            font-weight: 600;
            font-size: 32rpx;
            color: #000000;
            line-height: 32rpx;
            text-align: right;
          }
          .user-2 {
            color: #999999;
          }
          .order {
            height: 22rpx;
            font-size: 22rpx;
            color: #999999;
            line-height: 22rpx;
            margin-top: 24rpx;
            text-align: right;
          }
        }
        .ic {
          width: 20rpx;
          height: 20rpx;
          margin-left: 20rpx;
        }
      }
    }
  }
}
.placeholder-box {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  width: 100%;
  height: 80%;
  .img {
    width: 378rpx;
    height: 308rpx;
  }
  .text {
    height: 32rpx;
    font-size: 32rpx;
    color: #333;
    line-height: 32rpx;
    margin-top: 48rpx;
  }
}

.custom-dialog-content {
  padding: 64rpx 48rpx;
  .title {
    line-height: 48rpx;
    text-align: center;
    font-weight: 500;
    font-size: 34rpx;
    color: #000;
  }
  .desc {
    font-size: 34rpx;
    color: #000;
    line-height: 48rpx;
    margin: 48rpx 0;
  }
  .agree-block {
    .btn-checked {
      .ic {
        width: 28rpx;
        height: 28rpx;
        margin-right: 8rpx;
      }
    }
    .btn-text {
      font-weight: 500;
      font-size: 28rpx;
      color: #54d601;
      line-height: 28rpx;
      margin-right: 10rpx;
    }
  }

  .btn-confirm {
    width: 100%;
    line-height: 96rpx;
    text-align: center;
    border-radius: 48rpx;
    margin-top: 22rpx;
    color: #333;
    background: linear-gradient(to right, #ffff82, #bafd8f);
  }
}
