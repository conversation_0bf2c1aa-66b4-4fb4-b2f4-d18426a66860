import { couponShopList } from '@/api/index';

Component({
  properties: {
    latitude: {
      type: String,
      value: '',
    },
    longitude: {
      type: String,
      value: '',
    },
  },
  data: {
    list: [],
    loading: true,
  },
  lifetimes: {
    attached() {
      this.getStoreList();
    },
  },
  methods: {
    async getStoreList() {
      wx.showLoading({ title: '加载中...', mask: true });
      const { longitude, latitude } = this.data;
      const res = await couponShopList({ longitude, latitude });
      const { success, data } = res;
      this.setData({
        loading: false,
        list: success && data ? data : [],
      });
      wx.hideLoading({ noConflict: true });
    },
    onSelectAddress(e) {
      const { item } = e.currentTarget.dataset;
      const eventChannel = this.getOpenerEventChannel();
      eventChannel.emit('acceptStoreInfo', { store: item });
      wx.navigateBack();
    },
    onAddStore() {
      wx.navigateTo({ url: '/subpages/activity/store-info/index' });
    },
  },
});
