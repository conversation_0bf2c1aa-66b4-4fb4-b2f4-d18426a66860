.page {
  padding: 0 48rpx 300rpx;
  .list {
    .list-item {
      padding: 48rpx 0;
      border-bottom: 1rpx solid rgba(144, 152, 169, 0.35);
      .name {
        height: 32rpx;
        font-size: 32rpx;
        color: #000;
        line-height: 32rpx;
      }
      .address {
        height: 24rpx;
        font-size: 24rpx;
        color: #999;
        line-height: 24rpx;
        margin-top: 24rpx;
      }
      .cate {
        height: 24rpx;
        font-size: 24rpx;
        color: #666;
        line-height: 24rpx;
      }
    }
    .empty-block {
      display: flex;
      align-items: center;
      justify-content: center;
      flex: 1;
      height: 300rpx;
    }
  }
}
