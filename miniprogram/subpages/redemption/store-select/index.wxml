<t-navbar title="选择门店" left-arrow />

<scroll-view scroll-y style="flex: 1" show-scrollbar="{{ false }}">
  <view class="page">
    <view class="list">
      <view
        wx:for="{{ list }}"
        key="index"
        class="flex items-center justify-between list-item"
        data-item="{{ item }}"
        data-index="{{ index }}"
        bindtap="onSelectAddress"
      >
        <view class="flex justify-center flex-col">
          <view class="name">{{ item.name }}</view>
          <view class="address">{{ item.title }}*{{ item.couponCount }}{{ item.unitsText }}</view>
        </view>
        <view class="cate">{{ item.distance }}km</view>
      </view>
      <view wx:if="{{ !loading && list.length === 0 }}" class="empty-block">暂无数据</view>
    </view>
  </view>
</scroll-view>
