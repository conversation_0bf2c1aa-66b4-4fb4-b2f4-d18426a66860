import { setUserInfo, setToken } from '@/utils/auth';
import { isMobile } from '@/utils/validate';
import { customerRegister, sendVerifyCode } from '@/api/index';

const DEFAULT_COUNTDOWN = 60;
const app = getApp();

Component({
  properties: {
    showHome: {
      type: Boolean,
      value: false,
    },
  },
  data: {
    logoUrl: '/assets/image/logo/logo-light.png',
    countdown: 0,
    timer: null,
    mobile: '',
    code: '',
  },
  lifetimes: {
    detached() {
      const { timer } = this.data;
      if (timer) {
        clearInterval(timer);
        this.setData({ timer: null });
      }
    },
  },
  methods: {
    onInputChange(e) {
      const { type } = e.currentTarget.dataset;
      this.setData({
        [type]: e.detail.value,
      });
    },
    // 倒计时
    initCountdown() {
      const { timer } = this.data;
      if (timer) {
        clearTimeout(timer);
      }

      const endTime = Date.now() + this.data.countdown * 1000;

      const runCountdown = () => {
        const remaining = Math.round((endTime - Date.now()) / 1000);

        if (remaining <= 0) {
          this.setData({ countdown: 0 });
          return;
        }

        this.setData({
          countdown: remaining,
          timer: setTimeout(runCountdown, 1000),
        });
      };

      runCountdown();
    },
    async getCode() {
      const { mobile, timer } = this.data;

      if (!mobile || !isMobile(mobile)) {
        wx.showToast({
          title: '请输入正确的手机号',
          icon: 'none',
        });
        return;
      }

      // Start countdown
      this.setData({
        countdown: DEFAULT_COUNTDOWN,
      });

      wx.showLoading({ title: '发送中...' });

      const res = await sendVerifyCode({ mobile });
      if (res.success) {
        this.initCountdown();
        wx.showToast({ title: '验证码已发送', icon: 'none' });
      } else {
        // 重置倒计时
        this.setData({ countdown: 0 });
        if (timer) {
          clearInterval(timer);
        }
      }
    },

    async handleLogin() {
      const { mobile, code } = this.data;
      if (!mobile || !isMobile(mobile)) {
        wx.showToast({
          title: '请输入正确的手机号',
          icon: 'none',
        });
        return;
      }
      if (!code) {
        wx.showToast({
          title: '请输入验证码',
          icon: 'none',
        });
        return;
      }
      const { openId, unionId } = app.globalData.userInfo;

      const res = await customerRegister({ mobile, code, openId, unionId });

      if (res.success) {
        wx.showToast({ title: '登录成功', icon: 'none' });
        const newUserInfo = { ...app.globalData.userInfo, ...res.data };

        app.globalData.userInfo = newUserInfo;
        app.globalData.isLoggedIn = true;
        setUserInfo(newUserInfo);
        setToken(newUserInfo.token);
        app.globalEvent.emit('acceptLoginInfo', { user: newUserInfo });

        if (this.data.showHome) {
          wx.switchTab({ url: '/pages/home/<USER>' });
        } else {
          wx.navigateBack({ delta: 2 });
        }
      }
    },
  },
});
