import { customerRegister2 } from '@/api/index';
import { setUserInfo, setToken } from '@/utils/auth';

const app = getApp();

const validatePhoneInfo = ({ code, encryptedData, iv }) => {
  if (!code && !encryptedData && !iv) {
    wx.showModal({
      title: '提示',
      content: '需要授权手机号才能继续，请重新点击并授权！',
      showCancel: false,
      confirmColor: '#54d601',
    });
    return false;
  } else if (!code && encryptedData && iv) {
    // 微信版本过低
    wx.showModal({
      title: '提示',
      content: '当前微信版本不支持快捷登录，请手动输入手机号！',
      confirmColor: '#54d601',
      success: (res) => {
        if (res.confirm) {
          this.goMobileLogin();
        }
      },
    });
    return false;
  }
  return true;
};

Component({
  properties: {
    showHome: {
      type: Boolean,
      value: false,
    },
  },
  data: {
    logoUrl: '/assets/image/logo/logo-light.png',
  },
  lifetimes: {
    // attached() {
    //   console.log('this.data.showHome', this.data.showHome);
    // },
    detached() {
      if (!this.isLoggedIn) {
        console.log('移除');

        app.globalEvent.emit('acceptLoginInfo', { user: null });
      }
    },
  },
  methods: {
    // 一键登录-获取手机号
    onGetPhoneNumber(e) {
      if (!validatePhoneInfo(e.detail)) return;

      const { code } = e.detail;
      const { openId, unionId } = app.globalData.userInfo;
      const params = { code, openId, unionId };

      this.handleRegisterLogin(params);
    },
    // 注册并登录
    async handleRegisterLogin(params) {
      wx.showLoading({ title: '登录中' });
      const res = await customerRegister2(params);
      wx.hideLoading({ noConflict: true });

      if (res.success) {
        wx.showToast({ title: '登录成功' });

        const newUserInfo = { ...app.globalData.userInfo, ...res.data };

        app.globalData.userInfo = newUserInfo;
        app.globalData.isLoggedIn = true;
        setUserInfo(newUserInfo);
        setToken(newUserInfo.token);
        this.isLoggedIn = true;
        app.globalEvent.emit('acceptLoginInfo', { user: newUserInfo });

        if (this.data.showHome) {
          wx.switchTab({ url: '/pages/home/<USER>' });
        } else {
          wx.navigateBack({ delta: 1 });
        }
      }
    },
    // 手机验证码登录
    goMobileLogin() {
      wx.navigateTo({ url: `/subpages/auth/mobile-login/index?showHome=${this.data.showHome}` });
    },
    goBack() {
      wx.navigateBack({ delta: 1 });
    },
    goHome() {
      wx.switchTab({ url: '/pages/home/<USER>' });
    },
  },
});
