page {
  background-color: #f2f2f2;
}

.page {
  position: relative;
  width: 100vw;
  height: 100vh;
  .top-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 290rpx;
  }

  .top-box-wrap {
    margin-top: 32rpx;
    padding: 32rpx;
    border-top-left-radius: 28rpx;
    border-top-right-radius: 28rpx;
    background-color: #fff;
    .name {
      font-weight: 500;
      font-size: 40rpx;
      color: #333;
      line-height: 45rpx;
      margin-bottom: 16rpx;
      .text {
        width: 120rpx;
        height: 24rpx;
        font-size: 24rpx;
        color: #666666;
        line-height: 24rpx;
      }
      .text2 {
        color: #ff6701;
      }
      .name-block {
        width: 80%;
      }
    }
    .item {
      display: flex;
      align-items: center;
      height: 24rpx;
      font-size: 24rpx;
      color: #666;
      line-height: 24rpx;
      margin-top: 16rpx;
      .label {
        font-weight: 500;
      }
      .ic {
        width: 20rpx;
        height: 20rpx;
      }
    }
  }
  .card-box-wrap {
    height: calc(100% - 483rpx - 230rpx);
    padding: 24rpx 32rpx 32rpx;
    background-color: #f2f2f2;

    .card-box {
      display: flex;
      justify-content: center;
      flex-direction: column;
      width: 686rpx;
      height: 100%;
      border-radius: 28rpx;
      margin: 0 auto;
      background-color: #fff;
      .tip {
        height: 28rpx;
        font-size: 28rpx;
        color: #333;
        line-height: 28rpx;
        text-align: center;
        padding-top: 32rpx;
      }
      .coupon-block {
        height: 40rpx;
        font-weight: 500;
        font-size: 40rpx;
        color: #333333;
        line-height: 40rpx;
        text-align: center;
        margin: 48rpx auto 24rpx;
      }

      .invalid-date {
        height: 28rpx;
        font-size: 28rpx;
        color: #ff6701;
        line-height: 28rpx;
        text-align: center;
      }
      .btn-unlock {
        width: 320rpx;
        height: 96rpx;
        border-radius: 48rpx;
        line-height: 96rpx;
        text-align: center;
        font-size: 32rpx;
        color: #fff;
        text-align: center;
        margin: 72rpx auto 0;
        background-color: #ff6701;
      }
      .tip2 {
        height: 24rpx;
        font-size: 24rpx;
        color: #999999;
        line-height: 24rpx;
        text-align: center;
      }
    }
  }

  .footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    background: #ffffff;
    padding: 36rpx 32rpx;
    padding-bottom: calc(36rpx + env(safe-area-inset-bottom));
    box-sizing: border-box;
    box-shadow: 0rpx -12rpx 20rpx 0rpx rgba(0, 0, 0, 0.05);
    .left {
      width: 480rpx;
      .shop-name {
        width: 100%;
        height: 28rpx;
        font-weight: 500;
        font-size: 28rpx;
        color: #666666;
        line-height: 28rpx;
        margin-bottom: 16rpx;
      }
      .address {
        width: 100%;
        height: 24rpx;
        font-size: 24rpx;
        color: #666666;
        line-height: 24rpx;
        .ic {
          min-width: 24rpx;
          width: 24rpx;
          height: 24rpx;
          margin-right: 8rpx;
        }
        .text {
          flex: 1;
        }
      }
    }
    .right {
      .item {
        font-size: 24rpx;
        color: #333333;
        text-align: center;
        .ic {
          width: 36rpx;
          height: 36rpx;
          margin-top: 10rpx;
        }
      }
      .item-phone {
        margin-left: 48rpx;
      }
    }
  }

  .placeholder-box {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    width: 100%;
    height: 80%;
    .img {
      width: 378rpx;
      height: 308rpx;
    }
    .text {
      height: 32rpx;
      font-size: 32rpx;
      color: #333;
      line-height: 32rpx;
      margin-top: 48rpx;
    }
  }
}

.rule-container {
  position: relative;
  width: 718rpx;
  background: #ffffff;
  border-radius: 28rpx;
  padding: 80rpx 48rpx 64rpx;
  box-sizing: border-box;
  .title {
    height: 44rpx;
    font-weight: 500;
    font-size: 44rpx;
    color: #333333;
    line-height: 44rpx;
    text-align: center;
    margin-bottom: 48rpx;
  }
  .sub-title {
    height: 28rpx;
    font-weight: 500;
    font-size: 28rpx;
    color: #333333;
    line-height: 28rpx;
    margin-top: 48rpx;
    margin-bottom: 16rpx;
  }
  .sub-content {
    font-size: 28rpx;
    color: #999999;
    line-height: 42rpx;
  }

  .btn-invite {
    position: relative;
    width: 544rpx;
    margin: 64rpx auto 0;
    .block {
      width: 100%;
      line-height: 96rpx;
      text-align: center;
      border-radius: 48rpx;
      background: linear-gradient(to right, #ffff82, #bafd8f);
      .text {
        margin-left: 16rpx;
        color: #000000;
      }
    }
    .button {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      width: 100%;
      opacity: 0;
    }
  }
  .close-btn {
    position: absolute;
    left: 50%;
    margin-left: -32rpx;
    bottom: calc(-1 * (48rpx + 84rpx));
  }
}

.rule-container2 {
  position: relative;
  width: 718rpx;
  background: #ffffff;
  border-radius: 28rpx;
  padding: 80rpx 48rpx 32rpx;
  box-sizing: border-box;
  .title {
    height: 44rpx;
    font-weight: 500;
    font-size: 44rpx;
    color: #333333;
    line-height: 44rpx;
    text-align: center;
    margin-bottom: 48rpx;
  }
  .sub-title {
    height: 28rpx;
    font-weight: 500;
    font-size: 28rpx;
    color: #333333;
    line-height: 28rpx;
    margin-bottom: 16rpx;
  }
  .sub-content {
    font-size: 28rpx;
    color: #999999;
    line-height: 42rpx;
    margin-bottom: 48rpx;
  }
  .close-btn {
    position: absolute;
    left: 50%;
    margin-left: -32rpx;
    bottom: calc(-1 * (48rpx + 84rpx));
  }
}
