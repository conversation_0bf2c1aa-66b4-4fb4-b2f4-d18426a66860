<view class="page">
  <custom-image class="top-bg" src="{{ activityInfo.imageUrl }}" />
  <t-navbar class="custom-navbar">
    <view slot="capsule" class="custom-capsule">
      <image
        class="custom-capsule__icon home"
        src="/assets/image/icon/home3.svg"
        aria-label="首页"
        bind:tap="goHome"
      />
    </view>
  </t-navbar>
  <block wx:if="{{ activityInfo.id }}">
    <view class="top-box-wrap">
      <view class="flex items-center justify-between name">
        <!-- prettier-ignore -->
        <text class="truncate name-block">{{ activityInfo.title }}*{{ activityInfo.quantity }}{{ activityInfo.unitsText }}（价值¥{{ activityInfo.retailPrice || 0}}）</text>
        <view class="flex justify-end text">
          已选 1<text class="text2">{{ activityInfo.unitsText }}</text>
        </view>
      </view>
      <view class="item">
        <text class="label">限制：</text>
        <text>{{ activityInfo.usedLimitsText }}</text>
      </view>
      <view class="item" bindtap="onShowRule">
        <text class="label">须知：</text>
        <text>{{ activityInfo.usedTimeLimitsText }}</text>
        <image class="ic" src="/assets/image/icon/arrow-right.png" />
      </view>
    </view>
    <view class="card-box-wrap">
      <view class="card-box">
        <view class="tip">朋友{{ couponInfo.mobile }}赠送</view>
        <view class="coupon-block">
          {{ couponInfo.usedStatus === 0 ? '免费领取' : '该券已被领取' }}
        </view>
        <block wx:if="{{ couponInfo.usedStatus === 0 }}">
          <view class="invalid-date">有效期至{{ couponInfo.endTime }}到期</view>
          <view class="btn-unlock" bindtap="onTapClaim">立即领取</view>
        </block>
        <view wx:else class="tip2">感谢参与</view>
      </view>
    </view>
    <view class="flex items-center justify-between footer">
      <view class="left">
        <view class="truncate shop-name">{{ activityInfo.shopName }}</view>
        <view class="flex items-center address">
          <image class="ic" src="/assets/image/icon/address.png" />
          <text class="truncate text">{{ activityInfo.address }}</text>
        </view>
      </view>
      <view class="flex items-center justify-center right">
        <view class="flex flex-col items-center item" bindtap="onTapLocation">
          <image class="ic" src="/assets/image/icon/nav.png" />
          <text>{{ activityInfo.distance }}km</text>
        </view>
        <view class="flex flex-col items-center item item-phone" bindtap="onCallPhone">
          <image class="ic" src="/assets/image/icon/phone.png" />
          <text>电话</text>
        </view>
      </view>
    </view>
  </block>
  <block wx:else>
    <view class="placeholder-box">
      <image class="img" src="/assets/image/illus/coupon.png" />
      <text class="text">暂无记录</text>
    </view>
  </block>
</view>

<t-popup
  visible="{{ showClaimPopup }}"
  bind:visible-change="onClaimVisibleChange"
  placement="center"
  close-on-overlay-click
>
  <view class="rule-container">
    <view class="title">使用须知</view>
    <view class="sub-title">有效期</view>
    <view class="sub-content">有效期至{{ couponInfo.endTime }}</view>
    <view class="sub-title">使用时间</view>
    <view class="sub-content">{{ couponInfo.usedTimeLimitsText }}</view>
    <view class="sub-title">使用规则</view>
    <text class="sub-content" space="ensp">{{ couponInfo.usedRules }}</text>

    <view class="btn-invite" bindtap="onClaimCoupon">
      <view class="flex items-center justify-center block">
        <text class="text">确定领取</text>
      </view>
    </view>
    <t-icon
      t-class="close-btn"
      name="close-circle"
      size="64rpx"
      color="#fff"
      bind:tap="onCloseClaimPopup"
    />
  </view>
</t-popup>

<t-popup
  visible="{{ showRulePopup }}"
  bind:visible-change="onRuleVisibleChange"
  placement="center"
  close-on-overlay-click
>
  <view class="rule-container2">
    <view class="title">购买须知</view>
    <view class="sub-title">使用限制</view>
    <view class="sub-content">{{ couponInfo.usedLimitsText }}</view>
    <view class="sub-title">使用时间</view>
    <view class="sub-content">{{ couponInfo.usedTimeLimitsText }}</view>
    <view class="sub-title">使用规则</view>
    <text class="sub-content" space="ensp">{{ couponInfo.usedRules }}</text>
    <t-icon
      t-class="close-btn"
      name="close-circle"
      size="64rpx"
      color="#fff"
      bind:tap="onCloseRulePopup"
    />
  </view>
</t-popup>
