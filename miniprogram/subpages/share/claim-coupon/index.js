import {
  activityDetail2,
  usedCouponDetail,
  receiveCoupon,
  saveActivityCouponShare,
} from '@/api/index';
import { useLocation } from '@/utils/location';

const app = getApp();

Component({
  isRecord: false,
  properties: {
    cid: {
      type: String,
      value: '',
    },
    aid: {
      type: String,
      value: '',
    },
    uid: {
      type: String,
      value: '',
    },
    s: {
      // m => merchant; c => customer
      type: String,
      value: '',
    },
  },
  data: {
    ic_share: '/assets/image/icon/share.png',
    ic_navigation: '/assets/image/icon/navigation.png',
    ic_phone: '/assets/image/icon/phone2.png',
    locationInfo: {},
    activityInfo: {},
    couponInfo: {},
    showClaimPopup: false,
    showRulePopup: false,
  },
  lifetimes: {
    attached() {
      this.init();
    },
  },
  methods: {
    async init() {
      const login = await app.ensureLogin(); // 确保登录

      if (login.success) {
        this.handleRecord();
      }
      this.handleGetInfo();
    },
    async handleGetInfo() {
      wx.showLoading({ title: '加载中...' });
      const loc = await useLocation();

      if (loc) {
        this.setData({ ...loc });
        this.getActivityInfo(loc);
        this.getCouponInfo();
      } else {
        wx.hideLoading({ noConflict: true });
      }
    },
    async handleRecord() {
      const { uid, cid } = this.data;
      console.log('claim: uid', uid, 'cid', cid);

      if (uid && cid) {
        this.isRecord = true;
        saveActivityCouponShare({ shareUserId: uid, couponId: cid });
      }
      // if (source === 'customer') {
      // 顾客分享
      // } else {
      //   // 商家分享
      //   if (uid && aid) saveActivityShare({ shareUserId: uid, activityId: aid });
      // }
    },
    async getActivityInfo(loc) {
      const res = await activityDetail2({
        id: this.data.aid,
        longitude: loc.longitude,
        latitude: loc.latitude,
      });

      if (res.success) {
        this.setData({ activityInfo: res.data });
      }

      wx.hideLoading({ noConflict: true });
    },
    async getCouponInfo() {
      const res = await usedCouponDetail({ id: this.data.cid });

      if (res.success) {
        this.setData({ couponInfo: res.data });
      }
    },
    onTapClaim() {
      this.setData({ showClaimPopup: true });
    },
    onClaimVisibleChange(e) {
      this.setData({ showClaimPopup: e.detail.visible });
    },
    onCloseClaimPopup() {
      this.setData({ showClaimPopup: false });
    },
    onShowRule() {
      this.setData({ showRulePopup: true });
    },
    onCloseRulePopup() {
      this.setData({ showRulePopup: false });
    },
    onRuleVisibleChange(e) {
      this.setData({ showRulePopup: e.detail.visible });
    },
    onCallPhone() {
      const { activityInfo } = this.data;
      wx.makePhoneCall({ phoneNumber: String(activityInfo.phone) });
    },
    onTapLocation() {
      const { longitude, latitude } = this.data.activityInfo || {};
      wx.openLocation({ latitude, longitude });
    },
    goHome() {
      wx.switchTab({ url: '/pages/home/<USER>' });
    },
    async onClaimCoupon() {
      const login = await app.checkLogin();

      if (login.success) {
        if (!this.isRecord) {
          this.handleRecord();
        }
        wx.showLoading({ title: '领取中...', mask: true });

        const { cid } = this.data;
        const res = await receiveCoupon({ id: cid });

        wx.hideLoading({ noConflict: true });
        if (res.success) {
          app.globalData.shouldReloadTabPage = true;
          wx.showToast({ title: '领取成功！', icon: 'none' });
          wx.switchTab({ url: '/pages/redemption/index' });
        }
      }
    },
  },
});
