import {
  activityDetail2,
  createActivityOrder,
  saveActivityShare,
  saveActivityCouponShare,
} from '@/api/index';
import { useLocation } from '@/utils/location';
import { getQrcodeParams } from '@/utils/util';

const app = getApp();
// 用户端显示优惠券详情规则：
// 1-已下架、未上架，显示为“1-已售罄”
// 2-推广中，未购买，显示为“2-未激活”
// 3-推广中，已购买，显示为“3-已购买”
// 4-推广中，但活动不限购，显示为“2-未激活”

// 来源 merchant-商家二维码 customer-顾客分享

Component({
  isRecord: false,
  properties: {
    // 页面传入
    latitude: {
      type: String,
      value: '',
    },
    longitude: {
      type: String,
      value: '',
    },
    // 页面 或 二维码传入
    id: {
      // 活动id
      type: String,
      value: '',
    },
    cid: {
      // 优惠券id
      type: String,
      value: '',
    },
    // 用户id
    uid: {
      type: String,
      value: '',
    },
    q: {
      type: String,
      value: '',
    },
    // m => merchant; c => customer
    s: {
      type: String,
      value: '',
    },
  },
  data: {
    info: null,
    couponList: [],
    showRulePopup: false,
    isHome: false,
  },
  pageLifetimes: {
    show() {
      this.init();
    },
  },
  // lifetimes: {
  //   attached() {
  //     // this.init();
  //   },
  // },
  methods: {
    async init() {
      // 提前解析二维码参数
      const qrParams = getQrcodeParams(this.data.q);

      if (qrParams && qrParams.id) {
        const { id, uid } = qrParams;

        // 用 Promise 包一层，等 setData 完成再继续
        await new Promise((resolve) => {
          this.setData({ id, uid }, resolve);
        });
      }

      wx.showLoading();
      // 登录流程
      await app.ensureLogin();
      const login = await app.ensureLogin(); // 确保登录
      wx.hideLoading({ noConflict: true });
      const { q, uid } = this.data;

      this.setData({ isHome: q || uid });
      this.getLocation();

      if (login.success) {
        this.handleRecord();
      }
    },
    async getLocation() {
      const { latitude, longitude } = this.data;

      if (!latitude || !longitude) {
        wx.showLoading();
        const loc = await useLocation();
        wx.hideLoading({ noConflict: true });

        this.setData(
          {
            latitude: loc.latitude,
            longitude: loc.longitude,
          },
          () => this.getInfo()
        );
        return;
      }
      this.getInfo();
    },
    async getInfo() {
      wx.showLoading({ title: '加载中...' });

      const { latitude, longitude, id, uid, q } = this.data;

      const res = await activityDetail2({ id, longitude, latitude });

      if (res.success) {
        this.setData({ info: res.data });
      }

      wx.hideLoading({ noConflict: true });
    },
    handleRecord() {
      this.isRecord = true;
      const { id, uid, cid, s } = this.data;
      // console.log('buy: uid', uid, 'cid', cid, 'id', id, 's', s);

      if (s === 'c') {
        // 顾客分享;
        if (uid && cid) saveActivityCouponShare({ shareUserId: uid, couponId: cid });
      } else {
        // 商家分享
        if (uid && id) saveActivityShare({ shareUserId: uid, activityId: id });
      }
    },
    async handleCreateOrder() {
      wx.showLoading({ title: '提交中...', mask: true });
      const res = await createActivityOrder({ id: this.data.id });

      if (res.success) {
        wx.navigateTo({
          url: `/subpages/home/<USER>/index?orderId=${res.data.orderId}`,
        });
      }
      wx.hideLoading({ noConflict: true });
    },
    async onTapBuy() {
      const login = await app.checkLogin();
      if (login.success) {
        if (!this.isRecord) {
          this.handleRecord();
        }

        this.handleCreateOrder();
      }
    },
    onCallPhone() {
      const { phone } = this.data.info || {};

      if (phone) {
        wx.makePhoneCall({ phoneNumber: String(phone) });
      }
    },
    onShowRule() {
      this.setData({ showRulePopup: true });
    },
    onCloseRulePopup() {
      this.setData({ showRulePopup: false });
    },
    onVisibleChange(e) {
      this.setData({ showRulePopup: e.detail.visible });
    },
    onTapLocation() {
      const { longitude, latitude } = this.data.info;
      wx.openLocation({ latitude, longitude });
    },
    goBack() {
      wx.navigateBack({ delta: 1 });
    },
    goHome() {
      wx.switchTab({ url: '/pages/home/<USER>' });
    },
  },
});
