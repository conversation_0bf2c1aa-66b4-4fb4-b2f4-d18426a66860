<scroll-view class="scrollarea" style="flex: 1" scroll-y>
  <block wx:if="{{ info }}">
    <view class="page">
      <view class="cover">
        <custom-image class="img" src="{{ info.imageUrl }}" />
        <view class="mask"></view>
      </view>
      <t-navbar class="custom-navbar">
        <view slot="capsule" class="custom-capsule">
          <image
            wx:if="{{ !isHome }}"
            class="custom-capsule__icon back"
            src="/assets/image/icon/arrow-left2.svg"
            aria-label="返回"
            bind:tap="goBack"
          />
          <image
            wx:else
            class="custom-capsule__icon home"
            src="/assets/image/icon/home3.svg"
            aria-label="首页"
            bind:tap="goHome"
          />
        </view>
      </t-navbar>
      <view class="cover-placeholder"></view>
      <view class="merchant-box">
        <view class="flex items-center justify-between name">
          <view class="text">{{ info.shopName || '--' }}</view>
          <view class="flex items-center" bindtap="onShowRule">
            <text class="note">购买须知</text>
            <image class="ic" src="/assets/image/icon/arrow-right.png" />
          </view>
        </view>
        <view class="flex items-center justify-between address">
          <view class="left">
            <view class="flex items-center">
              <image class="ic" src="/assets/image/icon/address.png" />
              <text class="text">{{ info.address || '--' }}</text>
            </view>
            <view class="tag">
              {{ info.cityName || '--' }} | {{ info.largeCategoryName || '--' }}
            </view>
          </view>
          <view class="flex items-center justify-between right">
            <view class="flex items-center flex-col item" bindtap="onTapLocation">
              <image class="ic" src="/assets/image/icon/nav.png" />
              <text class="text">{{ info.distance || 0 }}km</text>
            </view>
            <view class="flex items-center flex-col item" bindtap="onCallPhone">
              <image class="ic" src="/assets/image/icon/phone.png" />
              <text class="text">电话</text>
            </view>
          </view>
        </view>
      </view>

      <view class="coupon-list">
        <view
          wx:for="{{ info.items }}"
          wx:key="id"
          class="flex items-center justify-between coupon-item"
        >
          <view class="flex items-center">
            <custom-image class="img" src="{{ item.imageUrl }}" />
            <view class="flex flex-col info">
              <text class="truncate name">{{ item.title || '--' }}</text>
              <text class="desc">{{ item.unlockCycleDaysText || '--' }}</text>
              <text class="price">价值¥{{ item.retailPrice || 0 }}</text>
            </view>
          </view>
          <view class="state">{{ item.statusText || '--' }}</view>
        </view>
      </view>
    </view>
    <view class="flex items-center justify-between footer">
      <view class="left">
        <view class="flex items-end price">
          <text class="symbol">¥</text>
          <text class="val">{{ info.activityPrice || 0 }}/</text>
          <text class="num">{{ info.quantity || 0 }}{{ info.unitsText }} </text>
        </view>
        <text class="text">已售{{ info.orderQuantity || 0 }}</text>
      </view>
      <view class="right">
        <!-- 活动状态：1-待发布;2-推广中;3-已下架, 详见枚举 ActActivityStatus	 -->
        <view wx:if="{{ info.status == 2 }}" class="btn-buy" bindtap="onTapBuy">去付款</view>
        <view wx:else class="btn-buy btn-soldout">已售罄</view>
      </view>
    </view>
  </block>
  <block wx:else>
    <t-navbar class="custom-navbar">
      <view slot="capsule" class="custom-capsule">
        <image
          wx:if="{{ !isHome }}"
          class="custom-capsule__icon back"
          src="/assets/image/icon/arrow-left2.svg"
          aria-label="返回"
          bind:tap="goBack"
        />
        <image
          wx:else
          class="custom-capsule__icon home"
          src="/assets/image/icon/home3.svg"
          aria-label="首页"
          bind:tap="goHome"
        />
      </view>
    </t-navbar>
    <view class="empty-page">
      <custom-empty text="" />
    </view>
  </block>
</scroll-view>

<!-- t-class="sharecard-root" -->
<t-popup
  visible="{{ showRulePopup }}"
  bind:visible-change="onVisibleChange"
  placement="center"
  close-on-overlay-click
>
  <view class="rule-container">
    <view class="title">购买须知</view>
    <view class="sub-title">使用限制</view>
    <view class="sub-content">{{ info.usedLimitsText }}</view>
    <view class="sub-title">使用时间</view>
    <view class="sub-content">{{ info.usedTimeLimitsText }}</view>
    <view class="sub-title">使用规则</view>
    <text class="sub-content" space="ensp">{{ info.usedRules }}</text>
    <t-icon
      t-class="close-btn"
      name="close-circle"
      size="64rpx"
      color="#fff"
      bind:tap="onCloseRulePopup"
    />
  </view>
</t-popup>
