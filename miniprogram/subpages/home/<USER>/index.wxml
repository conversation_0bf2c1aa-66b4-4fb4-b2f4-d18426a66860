<t-navbar title="提交订单" left-arrow />
<scroll-view class="scrollarea">
  <view class="page">
    <view class="content-wrap">
      <view class="order-box">
        <view class="top-block">
          <custom-image class="img" src="{{ info.imageUrl }}" />
          <view class="right-block">
            <view class="name">{{ info.title }} ({{ info.quantity }}{{ info.unitsText }})</view>
            <view class="flex items-center tip">
              <text class="title">限制</text><text>：{{ info.usedLimitsText }}</text>
            </view>
            <view class="flex items-center tip" bindtap="onTapNotice">
              <view class="flex items-center"
                ><text class="title">须知</text> <text>：{{ info.usedTimeLimitsText }}</text></view
              >
              <image class="ic" src="{{ ic_arrow }}" />
            </view>
          </view>
        </view>

        <view class="center-block">
          <view class="flex items-center label">
            <text>商品总价</text>
            <text class="num"> (共{{ info.quantity }}{{ info.unitsText }})</text>
          </view>
          <view class="value">
            <view class="price">
              <view class="symbol">¥</view>
              <view class="val">{{ info.activityPrice }}</view>
            </view>
          </view>
        </view>
        <view class="bottom-block">
          <view class="item">
            <view class="label">优惠合计</view>
            <view class="flex items-center value">
              <text style="color: #333">共：</text>
              <text>-¥{{ info.discountPrice }}</text>
            </view>
          </view>
          <view class="item">
            <view class="label">有券共省</view>
            <view class="value">-¥{{ info.discountPrice }}</view>
          </view>
          <view class="flex items-center justify-end item2">
            <text class="label">实际支付：</text>
            <text class="val">¥ {{ info.activityPrice }}</text>
          </view>
        </view>
      </view>
      <view class="tips">支付后请在核销页查看，消费时出示核销码即可</view>
    </view>

    <view class="flex items-center justify-between footer">
      <view class="left">
        <view class="flex items-end price">
          <text class="symbol">¥</text>
          <text class="val">{{ info.activityPrice }}</text>
        </view>
        <text class="text">共{{ info.quantity }}份 已优惠 ¥ {{ info.discountPrice }}</text>
      </view>
      <view class="right">
        <view class="btn-buy" bindtap="onSubmit">提交订单</view>
      </view>
    </view>
  </view>
</scroll-view>
