/**
 * 提交订单页面
 *
 * 功能：
 * 1. 显示订单详情
 * 2. 提交订单并发起支付
 * 3. 支付成功后订阅消息
 */
import { orderDetail, cashier, templateList, saveSubscribeMessage } from '@/api/index';

// 支付相关常量
const PAY_TYPE = {
  WECHAT: 1, // 微信支付
  ALIPAY: 2, // 支付宝
};

const TRADE_TYPE = {
  H5: 1, // 手机H5网站
  PC: 2, // 电脑网站支付
  SCAN: 3, // 扫码支付
  JSAPI: 4, // JSAPI支付（微信H5即公众号支付或小程序支付）
  APP: 5, // APP支付
};

const app = getApp();

Component({
  properties: {
    orderId: {
      type: String,
      value: '',
    },
  },

  lifetimes: {
    attached() {
      this.initPage();
    },
  },

  data: {
    loading: false, // 页面加载状态
    info: {}, // 订单信息
    templateList: [], // 消息模板列表
  },

  methods: {
    /**
     * 页面初始化
     */
    async initPage() {
      try {
        // 并行请求订单信息和消息模板
        await Promise.all([this.getOrderInfo(), this.getTemplateList()]);
      } catch (error) {
        console.error('页面初始化失败:', error);
        wx.showToast({ title: '加载失败，请重试', icon: 'none' });
      }
    },

    /**
     * 获取订单详情
     */
    async getOrderInfo() {
      this.setData({ loading: true });

      try {
        wx.showLoading({ title: '加载中...', mask: true });
        const res = await orderDetail({ id: this.data.orderId });

        if (res.success && res.data) {
          // 计算折扣价格
          const discountPrice = (
            res.data.retailPrice * res.data.quantity -
            res.data.activityPrice
          ).toFixed(2);

          this.setData({
            info: {
              ...res.data,
              discountPrice,
            },
          });
        } else {
          wx.showToast({ title: res.message || '获取订单失败', icon: 'none' });
        }
      } catch (error) {
        console.error('获取订单详情失败:', error);
        wx.showToast({ title: '获取订单失败', icon: 'none' });
      } finally {
        this.setData({ loading: false });
        wx.hideLoading({ noConflict: true });
      }
    },

    /**
     * 获取消息模板列表
     */
    async getTemplateList() {
      try {
        const res = await templateList();
        if (res.success && Array.isArray(res.data)) {
          this.setData({ templateList: res.data });
        } else {
          console.warn('获取消息模板列表失败:', res.message);
        }
      } catch (error) {
        console.error('获取消息模板列表出错:', error);
      }
    },

    /**
     * 请求用户订阅消息
     * @returns {Promise<Object>} 订阅结果，包含用户接受的模板ID
     */
    requestSubscribeMessage() {
      return new Promise((resolve) => {
        const tmplIds = this.data.templateList.map((item) => item.templateId).filter(Boolean);

        if (!tmplIds.length) {
          console.log('没有可订阅的消息模板');
          resolve({ acceptedTemplateIds: [] });
          return;
        }

        wx.requestSubscribeMessage({
          tmplIds,
          success(res) {
            console.log('订阅消息结果:', res);

            // 筛选出用户接受的模板ID
            const acceptedTemplateIds = tmplIds.filter((id) => res[id] === 'accept');
            console.log('用户接受的模板:', acceptedTemplateIds);

            resolve({
              rawResult: res,
              acceptedTemplateIds,
            });
          },
          fail(err) {
            console.warn('订阅消息失败:', err);
            resolve({ acceptedTemplateIds: [] });
          },
          complete() {
            // 确保Promise总是被resolve
          },
        });
      });
    },

    /**
     * 保存用户订阅的消息ID到服务器
     * @param {Array} acceptedTemplateIds - 用户接受订阅的模板ID数组
     * @returns {Promise} 保存结果
     */
    async saveUserSubscriptions(acceptedTemplateIds = []) {
      // 如果没有接受的模板，则不需要保存
      if (!acceptedTemplateIds.length) {
        console.log('用户未接受任何订阅，不保存到服务器');
        return null;
      }

      // 找出接受的模板对应的ID
      const acceptedIds = this.data.templateList
        .filter((item) => acceptedTemplateIds.includes(item.templateId))
        .map((item) => item.id)
        .filter(Boolean);

      if (!acceptedIds.length) {
        console.log('没有可保存的订阅消息ID');
        return null;
      }

      try {
        // 调用API保存订阅信息
        const res = await saveSubscribeMessage({ ids: acceptedIds });
        console.log('保存订阅消息结果:', res);
        return res;
      } catch (error) {
        console.error('保存订阅消息失败:', error);
        return null;
      }
    },

    /**
     * 处理订阅消息流程
     */
    async handleSubscriptionFlow() {
      try {
        // 1. 确保有模板列表
        if (!this.data.templateList.length) {
          await this.getTemplateList();
        }

        // 2. 请求用户订阅消息
        const { acceptedTemplateIds } = await this.requestSubscribeMessage();

        // 3. 只保存用户接受的订阅消息
        if (acceptedTemplateIds.length > 0) {
          await this.saveUserSubscriptions(acceptedTemplateIds);
        } else {
          console.log('用户未接受任何订阅，跳过保存步骤');
        }
      } catch (error) {
        console.error('订阅消息流程出错:', error);
        // 订阅流程出错不影响后续操作
      }
    },

    /**
     * 处理支付请求
     * @param {Object} cashierParams - 收银台参数
     */
    handlePayment(cashierParams) {
      if (!cashierParams || !cashierParams.weixinPayParameters) {
        wx.showToast({ title: '支付参数错误', icon: 'none' });
        return;
      }

      const that = this;
      const { weixinPayParameters: params } = cashierParams;

      wx.requestPayment({
        timeStamp: params.timeStamp,
        nonceStr: params.nonceStr,
        package: params.packageValue,
        signType: params.signType,
        paySign: params.paySign,
        async success() {
          wx.showToast({ title: '支付成功！', icon: 'success' });

          // 支付成功后处理订阅消息
          await that.handleSubscriptionFlow();

          // 继续原来的流程
          app.globalData.shouldReloadTabPage = true;
          wx.switchTab({ url: '/pages/redemption/index' });
        },
        fail() {
          wx.showToast({ title: '支付失败！', icon: 'none' });
        },
      });
    },

    /**
     * 提交订单
     */
    async onSubmit() {
      if (this.data.loading) return;

      const user = app.globalData.userInfo;
      if (!user || !user.openId) {
        wx.showToast({ title: '用户信息获取失败', icon: 'none' });
        return;
      }

      this.setData({ loading: true });
      wx.showLoading({ title: '提交中...', mask: true });

      try {
        const res = await cashier({
          openId: user.openId,
          orderId: this.data.orderId,
          payType: PAY_TYPE.WECHAT,
          tradeType: TRADE_TYPE.JSAPI,
        });

        if (res.success) {
          this.handlePayment(res.data);
        } else {
          wx.showToast({ title: res.message || '提交订单失败', icon: 'none' });
        }
      } catch (error) {
        console.error('提交订单失败:', error);
        wx.showToast({ title: '提交订单失败，请重试', icon: 'none' });
      } finally {
        this.setData({ loading: false });
        wx.hideLoading({ noConflict: true });
      }
    },

    /**
     * 调试订阅消息流程
     * 用于测试订阅消息功能
     */
    async onDebugSubscribe() {
      // 调试功能已启用
      wx.showLoading({ title: '调试中...', mask: true });

      try {
        // 1. 获取订阅消息模板
        await this.getTemplateList();
        console.log('模板列表:', this.data.templateList);

        // 2. 请求订阅消息
        const { acceptedTemplateIds, rawResult } = await this.requestSubscribeMessage();
        console.log('订阅结果:', rawResult);
        console.log('用户接受的模板:', acceptedTemplateIds);

        // 3. 保存用户接受的订阅消息
        if (acceptedTemplateIds.length > 0) {
          const saveResult = await this.saveUserSubscriptions(acceptedTemplateIds);
          console.log('保存结果:', saveResult);
        } else {
          console.log('用户未接受任何订阅，跳过保存步骤');
        }

        wx.showToast({ title: '调试完成', icon: 'success' });
      } catch (error) {
        console.error('调试订阅消息出错:', error);
        wx.showToast({ title: '调试失败', icon: 'error' });
      } finally {
        wx.hideLoading({ noConflict: true });
      }
    },
  },
});
