.page {
  position: relative;
  padding-bottom: 180rpx;
  background-color: #f2f2f2;
  .cover {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    height: 500rpx;
    .img {
      width: 100%;
      height: 100%;
    }
    .mask {
      position: absolute;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.2);
    }
  }
  .cover-placeholder {
    height: 294rpx;
  }
  .merchant-box {
    border-top-left-radius: 28rpx;
    border-top-right-radius: 28rpx;
    background-color: #fff;
    padding: 48rpx 32rpx;
    .name {
      min-height: 40rpx;
      font-size: 40rpx;
      .text {
        width: 530rpx;
        font-weight: 500;
        color: #333;
      }
      .note {
        font-size: 24rpx;
        color: #999;
      }
      .ic {
        width: 20rpx;
        height: 20rpx;
        margin-left: 8rpx;
        opacity: 0.6;
      }
    }
    .address {
      margin-top: 24rpx;
      .left {
        flex: 1;

        .ic {
          width: 24rpx;
          height: 24rpx;
        }
        .text {
          flex: 1;
          // height: 24rpx;
          font-size: 24rpx;
          color: #666;
          line-height: 26rpx;
          margin-left: 8rpx;
        }
        .tag {
          font-size: 24rpx;
          color: #999;
          margin-top: 24rpx;
        }
      }
      .right {
        // min-width: 200rpx;
        .item {
          margin-left: 32rpx;
          .ic {
            width: 28rpx;
            height: 28rpx;
          }
          .text {
            height: 18rpx;
            font-size: 18rpx;
            color: #333;
            line-height: 18rpx;
            text-align: center;
            margin-top: 8rpx;
          }
        }
      }
    }
  }
  // 优惠券
  .coupon-list {
    padding: 24rpx 32rpx;
    box-sizing: border-box;
    .coupon-item {
      padding: 24rpx;
      box-sizing: border-box;
      margin-bottom: 24rpx;
      background-color: #ffffff;
      border-radius: 28rpx;
      .img {
        width: 104rpx;
        height: 104rpx;
        background-color: #d8d8d8;
        border-radius: 14rpx;
      }
      .info {
        flex: 1;
        margin-left: 16rpx;
        .name {
          height: 24rpx;
          font-weight: 500;
          font-size: 24rpx;
          color: #333;
          line-height: 24rpx;
        }
        .desc {
          height: 20rpx;
          font-weight: 400;
          font-size: 20rpx;
          color: #666;
          line-height: 20rpx;
          margin-top: 16rpx;
        }
        .price {
          height: 22rpx;
          font-size: 22rpx;
          color: #999;
          line-height: 22rpx;
          text-decoration: line-through;
          margin-top: 16rpx;
        }
      }
      .state {
        width: 180rpx;
        height: 24rpx;
        font-size: 24rpx;
        color: #999;
        line-height: 24rpx;
        text-align: right;
      }
    }
  }
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 32rpx;
  padding-bottom: calc(constant(safe-area-inset-bottom) + 32rpx);
  padding-bottom: calc(env(safe-area-inset-bottom) + 32rpx);
  background-color: #fff;
  box-shadow: 0 -12rpx 20rpx 0 rgba(0, 0, 0, 0.05);
  .left {
    .price {
      color: #fa5151;

      .symbol {
        font-size: 24rpx;
        line-height: 1;
      }
      .val {
        font-family: Roboto, Roboto;
        font-weight: bold;
        font-size: 40rpx;
        line-height: 1;
      }
      .num {
        line-height: 1;
      }
    }
    .text {
      height: 20rpx;
      font-size: 20rpx;
      color: #666;
      margin-top: 16rpx;
    }
  }
  .right {
    .btn-buy {
      width: 200rpx;
      height: 72rpx;
      line-height: 72rpx;
      text-align: center;
      font-size: 28rpx;
      color: #000;
      border-radius: 48rpx;
      background: linear-gradient(to right, #ffff82, #bafd8f);
    }
    .btn-soldout {
      color: #999;
      background: #efefef;
    }
  }
}

.rule-container {
  position: relative;
  width: 718rpx;
  background: #ffffff;
  border-radius: 28rpx;
  padding: 80rpx 48rpx 32rpx;
  box-sizing: border-box;
  .title {
    height: 44rpx;
    font-weight: 500;
    font-size: 44rpx;
    color: #333333;
    line-height: 44rpx;
    text-align: center;
    margin-bottom: 48rpx;
  }
  .sub-title {
    height: 28rpx;
    font-weight: 500;
    font-size: 28rpx;
    color: #333333;
    line-height: 28rpx;
    margin-bottom: 16rpx;
  }
  .sub-content {
    font-size: 28rpx;
    color: #999999;
    line-height: 42rpx;
    margin-bottom: 48rpx;
  }
  .close-btn {
    position: absolute;
    left: 50%;
    margin-left: -32rpx;
    bottom: calc(-1 * (48rpx + 84rpx));
  }
}

.empty-page {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80vh;
}
