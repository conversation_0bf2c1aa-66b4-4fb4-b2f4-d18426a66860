.page {
  position: relative;
  padding-bottom: 180rpx;
  background-color: #f2f2f2;
  .content-wrap {
    padding: 24rpx 24rpx 120rpx;
    .order-box {
      padding: 32rpx 24rpx;
      margin-bottom: 24rpx;
      border-radius: 28rpx;
      background-color: #fff;
      .top-block {
        display: flex;
        align-items: center;
        .img {
          width: 104rpx;
          height: 104rpx;
          border-radius: 14rpx;
        }
        .right-block {
          margin-left: 18rpx;
          .name {
            height: 28rpx;
            font-weight: 500;
            font-size: 28rpx;
            color: #333;
            line-height: 28rpx;
          }
          .tip {
            margin-top: 16rpx;
            height: 20rpx;
            font-weight: 400;
            font-size: 20rpx;
            color: #666;
            line-height: 20rpx;
            .title {
              font-weight: 500;
            }
            .ic {
              width: 16rpx;
              height: 16rpx;
              margin-left: 12rpx;
            }
          }
        }
      }
      .center-block {
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-top: 1rpx solid #ccc;
        border-bottom: 1rpx solid #ccc;
        padding: 30rpx 0;
        margin: 24rpx 0;
        .label {
          height: 24rpx;
          font-size: 24rpx;
          color: #333;
          line-height: 24rpx;
          .num {
            color: #999;
          }
        }
        .value {
          .price {
            display: flex;
            align-items: flex-end;
            color: #333;
            .symbol {
              font-size: 24rpx;
              line-height: 1;
            }
            .val {
              margin-left: 2rpx;
              font-size: 32rpx;
              line-height: 1;
            }
          }
        }
      }
      .bottom-block {
        .item {
          display: flex;
          align-items: center;
          justify-content: space-between;

          margin-bottom: 32rpx;
          .label {
            height: 24rpx;
            font-weight: 500;
            font-size: 24rpx;
            color: #333;
            line-height: 24rpx;
          }
          .value {
            height: 24rpx;
            font-size: 24rpx;
            color: #fa5151;
            line-height: 24rpx;
          }
        }
        .item2 {
          .label {
            color: #333;
            margin-right: 14rpx;
          }
          .val {
            color: #fa5151;
          }
        }
      }
    }
    .tips {
      font-weight: 400;
      font-size: 24rpx;
      color: #666;
      line-height: 24rpx;
      padding-left: 24rpx;
    }
  }

  .footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 32rpx;
    padding-bottom: calc(constant(safe-area-inset-bottom) + 32rpx);
    padding-bottom: calc(env(safe-area-inset-bottom) + 32rpx);
    background-color: #fff;
    box-shadow: 0 -12rpx 20rpx 0 rgba(0, 0, 0, 0.05);
    .left {
      .price {
        color: #fa5151;

        .symbol {
          font-size: 24rpx;
          line-height: 1;
        }
        .val {
          height: 40rpx;
          font-family: Roboto, Roboto;
          font-weight: bold;
          font-size: 40rpx;
          line-height: 1;
        }
      }
      .text {
        height: 20rpx;
        font-size: 20rpx;
        color: #666;
        margin-top: 16rpx;
      }
    }
    .right {
      display: flex;
      align-items: center;

      .btn-debug {
        width: 160rpx;
        height: 72rpx;
        line-height: 72rpx;
        text-align: center;
        font-size: 28rpx;
        color: #fff;
        border-radius: 48rpx;
        background: #007aff;
        margin-right: 20rpx;
      }

      .btn-buy {
        width: 200rpx;
        height: 72rpx;
        line-height: 72rpx;
        text-align: center;
        font-size: 28rpx;
        color: #000;
        border-radius: 48rpx;
        background: linear-gradient(to right, #ffff82, #bafd8f);
      }
    }
  }
}
