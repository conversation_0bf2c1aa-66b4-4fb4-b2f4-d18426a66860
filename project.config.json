{"description": "项目配置文件", "miniprogramRoot": "miniprogram/", "compileType": "miniprogram", "libVersion": "trial", "npm": {"useNpm": true}, "packOptions": {"ignore": [], "include": []}, "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useCompilerPlugins": ["less"], "packNpmManually": true, "packNpmRelationList": [{"packageJsonPath": "package.json", "miniprogramNpmDistDir": "miniprogram/"}], "compileWorklet": false, "uglifyFileName": false, "uploadWithSourceMap": true, "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "condition": false, "swc": false, "disableSWC": true}, "condition": {}, "editorSetting": {"tabIndent": "auto", "tabSize": 2}, "appid": "wx41b448963efb766c", "srcMiniprogramRoot": "miniprogram/", "simulatorPluginLibVersion": {}}